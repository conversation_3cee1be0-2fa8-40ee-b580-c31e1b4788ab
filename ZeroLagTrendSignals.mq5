//+------------------------------------------------------------------+
//|                                         Zero Lag Trend Signals  |
//|                                  Copyright 2025, AlgoAlpha Port |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AlgoAlpha Port"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 7
#property indicator_plots   7

//--- Plot buffers
#property indicator_label1  "Zero Lag EMA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrAqua
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "Upper Band"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

#property indicator_label3  "Lower Band"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrLime
#property indicator_style3  STYLE_SOLID
#property indicator_width3  1

#property indicator_label4  "Bullish Trend Change"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrLime
#property indicator_width4  4

#property indicator_label5  "Bearish Trend Change"
#property indicator_type5   DRAW_ARROW
#property indicator_color5  clrRed
#property indicator_width5  4

#property indicator_label6  "Bullish Entry (Pullback)"
#property indicator_type6   DRAW_ARROW
#property indicator_color6  clrLime
#property indicator_width6  2

#property indicator_label7  "Bearish Entry (Pullback)"
#property indicator_type7   DRAW_ARROW
#property indicator_color7  clrRed
#property indicator_width7  2

//--- Input parameters
input int InpLength = 70;                    // Length for Zero Lag EMA
input double InpMultiplier = 1.2;            // Band Multiplier
input ENUM_TIMEFRAMES InpTF1 = PERIOD_M5;    // Timeframe 1
input ENUM_TIMEFRAMES InpTF2 = PERIOD_M15;   // Timeframe 2
input ENUM_TIMEFRAMES InpTF3 = PERIOD_H1;    // Timeframe 3
input ENUM_TIMEFRAMES InpTF4 = PERIOD_H4;    // Timeframe 4
input ENUM_TIMEFRAMES InpTF5 = PERIOD_D1;    // Timeframe 5
input color InpBullishColor = clrLime;       // Bullish Color
input color InpBearishColor = clrRed;        // Bearish Color
input bool InpShowTable = true;              // Show Multi-Timeframe Table
input bool InpEnableAlerts = true;           // Enable Alerts
input bool InpShowPriceLabels = true;        // Show Price Labels on Large Arrows

//--- Indicator buffers
double ZLEMABuffer[];
double UpperBandBuffer[];
double LowerBandBuffer[];
double BullishTrendBuffer[];
double BearishTrendBuffer[];
double BullishEntryBuffer[];
double BearishEntryBuffer[];

//--- Global variables
int ATRHandle;
int EMAHandle; // Add EMA handle for more accurate calculation
double ATRBuffer[];
double EMABuffer[]; // Add EMA buffer
int TrendState[];
int PrevTrendState[];
datetime LastAlertTime = 0;
bool PrevShowLabels = true;  // Track previous state of price labels setting

//--- Multi-timeframe variables
int MTF_Handles[5];
double MTF_Trends[5];
string MTF_Names[5] = {"M5", "M15", "H1", "H4", "D1"};
ENUM_TIMEFRAMES MTF_Periods[5];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set indicator buffers
    SetIndexBuffer(0, ZLEMABuffer, INDICATOR_DATA);
    SetIndexBuffer(1, UpperBandBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, LowerBandBuffer, INDICATOR_DATA);
    SetIndexBuffer(3, BullishTrendBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, BearishTrendBuffer, INDICATOR_DATA);
    SetIndexBuffer(5, BullishEntryBuffer, INDICATOR_DATA);
    SetIndexBuffer(6, BearishEntryBuffer, INDICATOR_DATA);
    
    //--- Set arrow codes
    PlotIndexSetInteger(3, PLOT_ARROW, 233); // Large up arrow for bullish trend change
    PlotIndexSetInteger(4, PLOT_ARROW, 234); // Large down arrow for bearish trend change
    PlotIndexSetInteger(5, PLOT_ARROW, 217); // Small up arrow for bullish entry (pullback)
    PlotIndexSetInteger(6, PLOT_ARROW, 218); // Small down arrow for bearish entry (pullback)
    
    //--- Set colors
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, InpBullishColor);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, InpBearishColor);
    PlotIndexSetInteger(5, PLOT_LINE_COLOR, InpBullishColor);
    PlotIndexSetInteger(6, PLOT_LINE_COLOR, InpBearishColor);
    
    //--- Initialize ATR handle
    ATRHandle = iATR(_Symbol, _Period, InpLength);
    if(ATRHandle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator");
        return INIT_FAILED;
    }
    
    //--- Initialize EMA handle for lag-compensated price
    // We'll calculate the lag-compensated price manually and use standard EMA
    // This will be more accurate than our manual EMA calculation
    
    //--- Initialize multi-timeframe periods
    MTF_Periods[0] = InpTF1;
    MTF_Periods[1] = InpTF2;
    MTF_Periods[2] = InpTF3;
    MTF_Periods[3] = InpTF4;
    MTF_Periods[4] = InpTF5;
    
    //--- Initialize multi-timeframe handles
    for(int i = 0; i < 5; i++)
    {
        MTF_Handles[i] = iCustom(_Symbol, MTF_Periods[i], "ZeroLagTrendSignals", 
                                InpLength, InpMultiplier, InpTF1, InpTF2, InpTF3, InpTF4, InpTF5,
                                InpBullishColor, InpBearishColor, false, false);
    }
    
    //--- Resize arrays
    ArrayResize(ATRBuffer, InpLength * 3);
    ArrayResize(TrendState, Bars(_Symbol, _Period));
    ArrayResize(PrevTrendState, Bars(_Symbol, _Period));
    //--- Initialize arrays
    ArrayInitialize(ZLEMABuffer, EMPTY_VALUE);
    ArrayInitialize(UpperBandBuffer, EMPTY_VALUE);
    ArrayInitialize(LowerBandBuffer, EMPTY_VALUE);
    ArrayInitialize(BullishTrendBuffer, EMPTY_VALUE);
    ArrayInitialize(BearishTrendBuffer, EMPTY_VALUE);
    ArrayInitialize(BullishEntryBuffer, EMPTY_VALUE);
    ArrayInitialize(BearishEntryBuffer, EMPTY_VALUE);
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release handles
    if(ATRHandle != INVALID_HANDLE)
        IndicatorRelease(ATRHandle);
        
    for(int i = 0; i < 5; i++)
    {
        if(MTF_Handles[i] != INVALID_HANDLE)
            IndicatorRelease(MTF_Handles[i]);
    }
    
    //--- Clean up price labels
    CleanupPriceLabels();
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- Check for minimum bars
    if(rates_total < InpLength + 10)
        return 0;
    //--- Check if price label setting has changed and clean up if needed
    if(!InpShowPriceLabels && PrevShowLabels)
    {
        CleanupPriceLabels();
    }
    PrevShowLabels = InpShowPriceLabels;
    //--- Get ATR data
    if(CopyBuffer(ATRHandle, 0, 0, rates_total, ATRBuffer) <= 0)
        return 0;
    //--- Calculate starting position
    int start = MathMax(prev_calculated - 1, InpLength);
    if(start < InpLength) start = InpLength;
    //--- Main calculation loop
    for(int i = start; i < rates_total; i++)
    {
        //--- Calculate Zero Lag EMA using a more accurate method
        int lag = (InpLength - 1) / 2;
        double lagCompensatedPrice = 0;
        if(i >= lag)
            lagCompensatedPrice = close[i] + (close[i] - close[i - lag]);
        else
            lagCompensatedPrice = close[i];
        if(i == InpLength)
        {
            double sum = 0;
            for(int j = 0; j < InpLength; j++)
            {
                int idx = i - j;
                double tempLagPrice = (idx >= lag) ? close[idx] + (close[idx] - close[idx - lag]) : close[idx];
                sum += tempLagPrice;
            }
            ZLEMABuffer[i] = sum / InpLength;
        }
        else if(i > InpLength)
        {
            double alpha = 2.0 / (InpLength + 1.0);
            ZLEMABuffer[i] = alpha * lagCompensatedPrice + (1.0 - alpha) * ZLEMABuffer[i-1];
        }
        else
        {
            ZLEMABuffer[i] = close[i];
        }
        double zlema = ZLEMABuffer[i];
        double volatility = CalculateVolatility(i) * InpMultiplier;
        int trend = 0;
        if(i > 0)
        {
            trend = TrendState[i-1];
        }
        else
        {
            trend = 0;
        }
        if(close[i] > zlema + volatility)
            trend = 1;
        else if(close[i] < zlema - volatility)
            trend = -1;
        TrendState[i] = trend;
        if(trend == 1)
        {
            LowerBandBuffer[i] = zlema - volatility;
            UpperBandBuffer[i] = EMPTY_VALUE;
        }
        else if(trend == -1)
        {
            UpperBandBuffer[i] = zlema + volatility;
            LowerBandBuffer[i] = EMPTY_VALUE;
        }
        else
        {
            UpperBandBuffer[i] = EMPTY_VALUE;
            LowerBandBuffer[i] = EMPTY_VALUE;
        }
        BullishTrendBuffer[i] = EMPTY_VALUE;
        BearishTrendBuffer[i] = EMPTY_VALUE;
        BullishEntryBuffer[i] = EMPTY_VALUE;
        BearishEntryBuffer[i] = EMPTY_VALUE;
        if(i > 0)
        {
            if(TrendState[i] == 1 && TrendState[i-1] <= 0)
            {
                BullishTrendBuffer[i] = zlema - volatility * 0.8;
                if(InpShowPriceLabels)
                {
                    string labelName = "BullSignal_" + IntegerToString(time[i]);
                    ObjectCreate(0, labelName, OBJ_TEXT, 0, time[i], close[i]);
                    ObjectSetString(0, labelName, OBJPROP_TEXT, "BUY " + DoubleToString(close[i], _Digits));
                    ObjectSetInteger(0, labelName, OBJPROP_COLOR, InpBullishColor);
                    ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
                    ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
                }
                else
                {
                    CleanupPriceLabels();
                }
                if(InpEnableAlerts && i == rates_total - 1)
                    SendAlert("Bullish Trend Change at " + DoubleToString(close[i], _Digits), time[i]);
            }
            if(TrendState[i] == -1 && TrendState[i-1] >= 0)
            {
                BearishTrendBuffer[i] = zlema + volatility * 0.8;
                if(InpShowPriceLabels)
                {
                    string labelName = "BearSignal_" + IntegerToString(time[i]);
                    ObjectCreate(0, labelName, OBJ_TEXT, 0, time[i], close[i]);
                    ObjectSetString(0, labelName, OBJPROP_TEXT, "SELL " + DoubleToString(close[i], _Digits));
                    ObjectSetInteger(0, labelName, OBJPROP_COLOR, InpBearishColor);
                    ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
                    ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
                }
                else
                {
                    CleanupPriceLabels();
                }
                if(InpEnableAlerts && i == rates_total - 1)
                    SendAlert("Bearish Trend Change at " + DoubleToString(close[i], _Digits), time[i]);
            }
        }
        if(i > 0)
        {
            if(TrendState[i] == 1 && TrendState[i-1] == 1 && 
               close[i] > zlema && close[i-1] <= ZLEMABuffer[i-1])
            {
                BullishEntryBuffer[i] = zlema - volatility * 1.2;
                if(InpEnableAlerts && i == rates_total - 1)
                    SendAlert("Bullish Entry Signal (Pullback)", time[i]);
            }
            if(TrendState[i] == -1 && TrendState[i-1] == -1 && 
               close[i] < zlema && close[i-1] >= ZLEMABuffer[i-1])
            {
                BearishEntryBuffer[i] = zlema + volatility * 1.2;
                if(InpEnableAlerts && i == rates_total - 1)
                    SendAlert("Bearish Entry Signal (Pullback)", time[i]);
            }
        }
        if(trend == 1)
            PlotIndexSetInteger(0, PLOT_LINE_COLOR, InpBullishColor);
        else if(trend == -1)
            PlotIndexSetInteger(0, PLOT_LINE_COLOR, InpBearishColor);
    }
    if(InpShowTable)
        UpdateMTFTable();
    return rates_total;
}

//+------------------------------------------------------------------+
//| Calculate Zero Lag EMA                                           |
//+------------------------------------------------------------------+
double CalculateZLEMA(const double &price[], int pos, int period)
{
    if(pos < period) return 0;
    
    int lag = (period - 1) / 2;
    double alpha = 2.0 / (period + 1.0);
    
    // Get the lag-compensated price
    double lagPrice = pos >= lag ? price[pos] + (price[pos] - price[pos - lag]) : price[pos];
    
    // Calculate EMA
    if(pos == period)
    {
        // Initialize with SMA
        double sum = 0;
        for(int i = 0; i < period; i++)
        {
            double tempLagPrice = (pos - i) >= lag ? 
                price[pos - i] + (price[pos - i] - price[pos - i - lag]) : price[pos - i];
            sum += tempLagPrice;
        }
        return sum / period;
    }
    else
    {
        // EMA calculation
        return alpha * lagPrice + (1 - alpha) * ZLEMABuffer[pos - 1];
    }
}

//+------------------------------------------------------------------+
//| Calculate Volatility (Highest ATR)                               |
//+------------------------------------------------------------------+
double CalculateVolatility(int pos)
{
    if(pos < InpLength * 3) return 0;
    
    double maxATR = 0;
    int lookback = InpLength * 3;
    
    for(int i = 0; i < lookback && (pos - i) >= 0; i++)
    {
        if(ATRBuffer[pos - i] > maxATR)
            maxATR = ATRBuffer[pos - i];
    }
    
    return maxATR;
}

//+------------------------------------------------------------------+
//| Update Multi-Timeframe Table                                     |
//+------------------------------------------------------------------+
void UpdateMTFTable()
{
    // This is a simplified version - in a full implementation,
    // you would create GUI objects to display the table
    
    // For now, we'll just update the comment
    string tableText = "=== Zero Lag MTF Signals ===\n";
    
    for(int i = 0; i < 5; i++)
    {
        // Get trend from multi-timeframe
        double mtfTrend = 0;
        if(MTF_Handles[i] != INVALID_HANDLE)
        {
            double buffer[1];
            if(CopyBuffer(MTF_Handles[i], 0, 0, 1, buffer) > 0)
                mtfTrend = buffer[0] > 0 ? 1 : -1;
        }
        
        string signal = mtfTrend == 1 ? "Bullish" : "Bearish";
        tableText += MTF_Names[i] + ": " + signal + "\n";
    }
    
    Comment(tableText);
}

//+------------------------------------------------------------------+
//| Send Alert                                                        |
//+------------------------------------------------------------------+
void SendAlert(string message, datetime currentTime)
{
    if(currentTime <= LastAlertTime) return;
    
    LastAlertTime = currentTime;
    
    string fullMessage = "[Zero Lag Signals] " + message + " on " + _Symbol + " " + 
                        EnumToString((ENUM_TIMEFRAMES)_Period);
    
    Alert(fullMessage);
    Print(fullMessage);
}

//+------------------------------------------------------------------+
//| ChartEvent function for handling GUI interactions                |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // Handle chart events if needed for GUI table
}

//+------------------------------------------------------------------+
//| Clean up price labels                                            |
//+------------------------------------------------------------------+
void CleanupPriceLabels()
{
    // Remove all signal labels from chart
    for(int i = ObjectsTotal(0, 0, OBJ_TEXT) - 1; i >= 0; i--)
    {
        string objName = ObjectName(0, i, 0, OBJ_TEXT);
        if(StringFind(objName, "BullSignal_") == 0 || StringFind(objName, "BearSignal_") == 0)
        {
            ObjectDelete(0, objName);
        }
    }
}
