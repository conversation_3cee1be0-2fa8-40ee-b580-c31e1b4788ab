//+------------------------------------------------------------------+
//|                                                   fxZeroTrend EA |
//|                                  Copyright 2025, cFox|
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AlgoAlpha Port"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade/Trade.mqh>

//--- Input parameters for the EA
input group "=== Trading Settings ==="
input double InpLotSize = 0.01;              // Lot Size
input int InpSlippagePoints = 10;             // Slippage in Points
input int InpMagicNumber = 123456;            // Magic Number
input string InpTradeComment = "fxZeroTrend"; // Trade Comment
input bool InpTFConfirm = true;               // Multi-Timeframe Confirmation

input group "=== Risk Management ==="
input int InpTakeProfitPoints = 0;            // Take Profit in Points (0 = disabled)
input int InpStopLossPoints = 0;              // Stop Loss in Points (0 = disabled)

input group "=== Zero Lag Settings ==="
input int InpLength = 70;                    // Zero Lag EMA Length
input double InpMultiplier = 1.2;            // Volatility Band Multiplier
input bool InpShowSignals = true;            // Show Buy/Sell Arrows on Chart
input bool InpEnableAlerts = true;           // Enable Alerts
input color InpBullishColor = clrLime;       // Bullish Color
input color InpBearishColor = clrRed;        // Bearish Color

input group "=== Multi-Timeframe Settings ==="
input bool InpShowMTFTable = true;          // Show Multi-Timeframe Table
input ENUM_TIMEFRAMES InpTF1 = PERIOD_M5;   // Timeframe 1
input ENUM_TIMEFRAMES InpTF2 = PERIOD_M15;  // Timeframe 2
input ENUM_TIMEFRAMES InpTF3 = PERIOD_H1;   // Timeframe 3
input ENUM_TIMEFRAMES InpTF4 = PERIOD_H4;   // Timeframe 4
input ENUM_TIMEFRAMES InpTF5 = PERIOD_D1;   // Timeframe 5

//--- Global variables
CTrade trade;
int ATRHandle;
double ZLEMABuffer[];
double ATRBuffer[];
int TrendState[];
datetime lastSignalTime = 0;
bool isTradeOpen = false;
ENUM_ORDER_TYPE currentTradeType = ORDER_TYPE_BUY;

//--- Multi-timeframe confirmation variables
bool pendingBuySignal = false;
bool pendingSellSignal = false;
datetime pendingSignalTime = 0;

//--- Multi-timeframe variables
int MTF_ATRHandles[5];
ENUM_TIMEFRAMES MTF_Periods[5];
string MTF_Names[5] = {"M5", "M15", "H1", "H4", "D1"};
int MTF_TrendValues[5]; // Current trend for each timeframe

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize the trade object
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetDeviationInPoints(InpSlippagePoints);
    
    //--- Create ATR handle
    ATRHandle = iATR(_Symbol, _Period, InpLength);
    if(ATRHandle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator");
        return INIT_FAILED;
    }
    
    //--- Initialize arrays
    ArraySetAsSeries(ZLEMABuffer, true);
    ArraySetAsSeries(ATRBuffer, true);
    ArraySetAsSeries(TrendState, true);
    
    //--- Initialize multi-timeframe analysis
    if(InpShowMTFTable)
    {
        InitializeMultiTimeframe();
    }

    //--- Check for existing trades
    CheckExistingTrades();

    Print("fxZeroTrend EA initialized successfully");
    Print("Magic Number: ", InpMagicNumber);
    Print("Lot Size: ", InpLotSize);
    Print("Zero Lag Length: ", InpLength);
    Print("Multiplier: ", InpMultiplier);
    Print("Show Signals: ", InpShowSignals);
    Print("Show MTF Table: ", InpShowMTFTable);
    Print("MTF Confirmation: ", InpTFConfirm ? "ENABLED" : "DISABLED");
    Print("Take Profit: ", InpTakeProfitPoints > 0 ? IntegerToString(InpTakeProfitPoints) + " points" : "DISABLED");
    Print("Stop Loss: ", InpStopLossPoints > 0 ? IntegerToString(InpStopLossPoints) + " points" : "DISABLED");

    //--- Calculate and display all historical signals
    if(InpShowSignals)
    {
        Print("Recreating all historical signal arrows...");
        RecreateAllSignalArrows();
    }

    //--- Update multi-timeframe table
    if(InpShowMTFTable)
    {
        UpdateMultiTimeframeTable();
    }

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release ATR handle
    if(ATRHandle != INVALID_HANDLE)
        IndicatorRelease(ATRHandle);

    //--- Release multi-timeframe handles
    for(int i = 0; i < 5; i++)
    {
        if(MTF_ATRHandles[i] != INVALID_HANDLE)
            IndicatorRelease(MTF_ATRHandles[i]);
    }

    //--- Clean up chart objects
    if(InpShowSignals)
        CleanupSignalArrows();

    if(InpShowMTFTable)
        CleanupMTFTable();

    Print("fxZeroTrend EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, _Period, 0);

    bool newBar = (currentBarTime != lastBarTime);

    if(newBar)
    {
        lastBarTime = currentBarTime;

        //--- Calculate Zero Lag signals
        if(!CalculateZeroLagSignals())
            return;

        //--- Recreate arrows on new bar (to ensure they persist)
        if(InpShowSignals)
            RecreateAllSignalArrows();

        //--- Update multi-timeframe analysis
        if(InpShowMTFTable)
            UpdateMultiTimeframeTable();

        //--- Check for pending signals that may now be confirmed
        CheckPendingSignals();

        //--- Check for trading signals
        CheckForSignals();
    }
}

//+------------------------------------------------------------------+
//| Calculate Zero Lag EMA and signals                              |
//+------------------------------------------------------------------+
bool CalculateZeroLagSignals()
{
    //--- Get required bars
    int bars = MathMax(InpLength * 4, 200);

    //--- Get price data (NOT as time series to match indicator)
    double close[];
    if(CopyClose(_Symbol, _Period, 0, bars, close) <= 0)
    {
        Print("Error copying close prices");
        return false;
    }

    //--- Get ATR data (NOT as time series to match indicator)
    if(CopyBuffer(ATRHandle, 0, 0, bars, ATRBuffer) <= 0)
    {
        Print("Error copying ATR data");
        return false;
    }

    //--- Resize arrays
    ArrayResize(ZLEMABuffer, bars);
    ArrayResize(TrendState, bars);

    //--- DO NOT set as time series - use normal array indexing like indicator
    ArraySetAsSeries(close, false);
    ArraySetAsSeries(ZLEMABuffer, false);
    ArraySetAsSeries(ATRBuffer, false);
    ArraySetAsSeries(TrendState, false);

    //--- Initialize arrays
    ArrayInitialize(ZLEMABuffer, 0);
    ArrayInitialize(TrendState, 0);

    //--- Calculate starting position (same as indicator)
    int start = InpLength;
    if(start < InpLength) start = InpLength;

    //--- Main calculation loop (same direction as indicator)
    for(int i = start; i < bars; i++)
    {
        //--- Calculate Zero Lag EMA using exact same method as indicator
        int lag = (InpLength - 1) / 2;
        double lagCompensatedPrice = 0;
        if(i >= lag)
            lagCompensatedPrice = close[i] + (close[i] - close[i - lag]);
        else
            lagCompensatedPrice = close[i];

        if(i == InpLength)
        {
            // Initialize with SMA (same as indicator)
            double sum = 0;
            for(int j = 0; j < InpLength; j++)
            {
                int idx = i - j;
                double tempLagPrice = (idx >= lag) ? close[idx] + (close[idx] - close[idx - lag]) : close[idx];
                sum += tempLagPrice;
            }
            ZLEMABuffer[i] = sum / InpLength;
        }
        else if(i > InpLength)
        {
            // EMA calculation (same as indicator)
            double alpha = 2.0 / (InpLength + 1.0);
            ZLEMABuffer[i] = alpha * lagCompensatedPrice + (1.0 - alpha) * ZLEMABuffer[i-1];
        }
        else
        {
            ZLEMABuffer[i] = close[i];
        }

        double zlema = ZLEMABuffer[i];
        double volatility = CalculateVolatility(i) * InpMultiplier;

        //--- Determine trend (same as indicator)
        int trend = 0;
        if(i > 0)
            trend = TrendState[i-1];

        if(close[i] > zlema + volatility)
            trend = 1;
        else if(close[i] < zlema - volatility)
            trend = -1;

        TrendState[i] = trend;



        //--- Store signal information (arrows will be created separately)
        // No need to create arrows here - they will be created by RecreateAllSignalArrows()

        //--- Debug print for recent bars
        if(i >= bars - 3)
        {
            Print("Bar ", i, " (", bars-1-i, " from current): Close=", close[i], " ZLEMA=", zlema,
                  " Volatility=", volatility, " Trend=", trend, " PrevTrend=", (i > 0 ? TrendState[i-1] : 0));
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check Multi-Timeframe Confirmation                              |
//+------------------------------------------------------------------+
bool CheckMTFConfirmation(int signalDirection)
{
    if(!InpTFConfirm)
        return true; // No confirmation required

    //--- Update all timeframe trends first
    for(int i = 0; i < 5; i++)
    {
        MTF_TrendValues[i] = CalculateTimeframeTrend(i);
    }

    int confirmingTFs = 0;
    int neutralTFs = 0;
    int opposingTFs = 0;

    //--- Count timeframes in each category
    for(int i = 0; i < 5; i++)
    {
        if(MTF_TrendValues[i] == signalDirection)
            confirmingTFs++;
        else if(MTF_TrendValues[i] == 0)
            neutralTFs++;
        else if(MTF_TrendValues[i] == -signalDirection)
            opposingTFs++;
    }

    //--- Log the analysis
    string directionStr = (signalDirection == 1) ? "BULLISH" : "BEARISH";
    Print("MTF Confirmation for ", directionStr, " signal:");
    Print("  Confirming TFs: ", confirmingTFs);
    Print("  Neutral TFs: ", neutralTFs);
    Print("  Opposing TFs: ", opposingTFs);

    //--- Decision logic
    if(confirmingTFs >= 2)
    {
        Print("✓ MTF Confirmation: ", confirmingTFs, " timeframes confirm the signal");
        return true;
    }
    else if(neutralTFs > 0 && opposingTFs == 0)
    {
        Print("⏳ MTF Waiting: Signal pending - ", neutralTFs, " neutral timeframes, waiting for confirmation");
        return false; // Wait for neutral timeframes to decide
    }
    else
    {
        Print("✗ MTF Rejection: Insufficient confirmation (", confirmingTFs, " confirming, ", opposingTFs, " opposing)");
        return false;
    }
}

//+------------------------------------------------------------------+
//| Check for pending signals that may now be confirmed             |
//+------------------------------------------------------------------+
void CheckPendingSignals()
{
    if(!InpTFConfirm)
        return;

    datetime currentTime = iTime(_Symbol, _Period, 1);

    //--- Check pending buy signal
    if(pendingBuySignal && !isTradeOpen)
    {
        if(CheckMTFConfirmation(1))
        {
            Print("*** PENDING BUY SIGNAL NOW CONFIRMED ***");
            OpenTrade(ORDER_TYPE_BUY, "MTF Confirmed Bullish Signal");
            pendingBuySignal = false;
            lastSignalTime = currentTime;
        }
        else
        {
            //--- Check if signal is too old (more than 5 bars)
            if(currentTime - pendingSignalTime > PeriodSeconds(_Period) * 5)
            {
                Print("Pending BUY signal expired after 5 bars");
                pendingBuySignal = false;
            }
        }
    }

    //--- Check pending sell signal
    if(pendingSellSignal && !isTradeOpen)
    {
        if(CheckMTFConfirmation(-1))
        {
            Print("*** PENDING SELL SIGNAL NOW CONFIRMED ***");
            OpenTrade(ORDER_TYPE_SELL, "MTF Confirmed Bearish Signal");
            pendingSellSignal = false;
            lastSignalTime = currentTime;
        }
        else
        {
            //--- Check if signal is too old (more than 5 bars)
            if(currentTime - pendingSignalTime > PeriodSeconds(_Period) * 5)
            {
                Print("Pending SELL signal expired after 5 bars");
                pendingSellSignal = false;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Recreate all historical signal arrows                           |
//+------------------------------------------------------------------+
void RecreateAllSignalArrows()
{
    if(!InpShowSignals)
        return;

    //--- Clean up existing arrows first
    CleanupSignalArrows();

    //--- Recalculate to get fresh data
    if(!CalculateZeroLagSignals())
        return;

    //--- Now create arrows for all historical signals
    int arraySize = ArraySize(TrendState);
    if(arraySize < 2)
        return;

    int arrowCount = 0;

    //--- Loop through all bars and create arrows for trend changes
    for(int i = InpLength + 1; i < arraySize; i++)
    {
        int prevTrend = TrendState[i-1];
        int currentTrend = TrendState[i];

        // Calculate bar index for time series (convert from normal array index to time series index)
        int barIndex = arraySize - 1 - i;
        datetime barTime = iTime(_Symbol, _Period, barIndex);

        if(barTime <= 0) continue; // Skip invalid times

        // Get ZLEMA and volatility for arrow positioning
        double zlema = ZLEMABuffer[i];
        double volatility = CalculateVolatility(i) * InpMultiplier;

        // Bullish trend change: from non-positive to positive
        if(currentTrend == 1 && prevTrend <= 0)
        {
            string arrowName = "BuyArrow_" + IntegerToString(barTime);
            double arrowPrice = zlema - volatility * 0.8;

            if(ObjectCreate(0, arrowName, OBJ_ARROW_UP, 0, barTime, arrowPrice))
            {
                ObjectSetInteger(0, arrowName, OBJPROP_COLOR, InpBullishColor);
                ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 3);
                ObjectSetInteger(0, arrowName, OBJPROP_ARROWCODE, 233);
                arrowCount++;
            }
        }

        // Bearish trend change: from non-negative to negative
        if(currentTrend == -1 && prevTrend >= 0)
        {
            string arrowName = "SellArrow_" + IntegerToString(barTime);
            double arrowPrice = zlema + volatility * 0.8;

            if(ObjectCreate(0, arrowName, OBJ_ARROW_DOWN, 0, barTime, arrowPrice))
            {
                ObjectSetInteger(0, arrowName, OBJPROP_COLOR, InpBearishColor);
                ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 3);
                ObjectSetInteger(0, arrowName, OBJPROP_ARROWCODE, 234);
                arrowCount++;
            }
        }
    }

    Print("Recreated ", arrowCount, " signal arrows on chart");
    ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| Calculate Volatility (Highest ATR) - Same as indicator          |
//+------------------------------------------------------------------+
double CalculateVolatility(int pos)
{
    if(pos < InpLength * 3) return (pos < ArraySize(ATRBuffer)) ? ATRBuffer[pos] : 0.001;

    double maxATR = 0;
    int lookback = InpLength * 3;

    for(int i = 0; i < lookback && (pos - i) >= 0; i++)
    {
        if(ATRBuffer[pos - i] > maxATR)
            maxATR = ATRBuffer[pos - i];
    }

    return (maxATR > 0) ? maxATR : 0.001;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckForSignals()
{
    //--- Check if we have enough data
    int arraySize = ArraySize(TrendState);
    if(arraySize < 3)
    {
        Print("Not enough trend data: ", arraySize);
        return;
    }

    //--- Get current time for signal validation
    datetime currentTime = iTime(_Symbol, _Period, 1); // Previous completed bar

    //--- Avoid duplicate signals on the same bar
    if(currentTime <= lastSignalTime)
        return;

    //--- With normal arrays, the most recent bars are at the end
    int currentBar = arraySize - 1;      // Most recent completed bar
    int previousBar = arraySize - 2;     // Previous bar

    //--- Debug: Print current trend states
    if(arraySize >= 3)
    {
        Print("Signal Check - Recent trends: [", previousBar, "]=", TrendState[previousBar],
              " [", currentBar, "]=", TrendState[currentBar]);
    }

    //--- Check for trend change signals (same logic as indicator)
    // Bullish trend change: from non-positive to positive
    if(currentBar >= 0 && previousBar >= 0 &&
       TrendState[currentBar] == 1 && TrendState[previousBar] <= 0)
    {
        Print("*** BULLISH TREND CHANGE DETECTED ***");
        Print("Previous bar trend: ", TrendState[previousBar], " -> Current completed bar trend: ", TrendState[currentBar]);
        Print("Time: ", TimeToString(currentTime));

        //--- Close any existing bearish trade
        if(isTradeOpen && currentTradeType == ORDER_TYPE_SELL)
            CloseTrade("Opposite signal detected");

        //--- Check MTF confirmation before opening trade
        if(!isTradeOpen)
        {
            if(InpTFConfirm)
            {
                if(CheckMTFConfirmation(1))
                {
                    OpenTrade(ORDER_TYPE_BUY, "MTF Confirmed Bullish Trend Change");
                    lastSignalTime = currentTime;
                }
                else
                {
                    Print("⏳ BUY signal pending MTF confirmation");
                    pendingBuySignal = true;
                    pendingSellSignal = false; // Cancel any pending sell
                    pendingSignalTime = currentTime;
                }
            }
            else
            {
                OpenTrade(ORDER_TYPE_BUY, "Bullish Trend Change");
                lastSignalTime = currentTime;
            }
        }

        if(InpEnableAlerts)
            Alert("[fxZeroTrend] Bullish trend change on ", _Symbol);
    }

    // Bearish trend change: from non-negative to negative
    if(currentBar >= 0 && previousBar >= 0 &&
       TrendState[currentBar] == -1 && TrendState[previousBar] >= 0)
    {
        Print("*** BEARISH TREND CHANGE DETECTED ***");
        Print("Previous bar trend: ", TrendState[previousBar], " -> Current completed bar trend: ", TrendState[currentBar]);
        Print("Time: ", TimeToString(currentTime));

        //--- Close any existing bullish trade
        if(isTradeOpen && currentTradeType == ORDER_TYPE_BUY)
            CloseTrade("Opposite signal detected");

        //--- Check MTF confirmation before opening trade
        if(!isTradeOpen)
        {
            if(InpTFConfirm)
            {
                if(CheckMTFConfirmation(-1))
                {
                    OpenTrade(ORDER_TYPE_SELL, "MTF Confirmed Bearish Trend Change");
                    lastSignalTime = currentTime;
                }
                else
                {
                    Print("⏳ SELL signal pending MTF confirmation");
                    pendingSellSignal = true;
                    pendingBuySignal = false; // Cancel any pending buy
                    pendingSignalTime = currentTime;
                }
            }
            else
            {
                OpenTrade(ORDER_TYPE_SELL, "Bearish Trend Change");
                lastSignalTime = currentTime;
            }
        }

        if(InpEnableAlerts)
            Alert("[fxZeroTrend] Bearish trend change on ", _Symbol);
    }
}

//+------------------------------------------------------------------+
//| Open a trade                                                     |
//+------------------------------------------------------------------+
void OpenTrade(ENUM_ORDER_TYPE orderType, string reason)
{
    //--- Check if trading is allowed
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        Print("Trading is not allowed in terminal");
        return;
    }

    if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
    {
        Print("Trading is not allowed for this EA");
        return;
    }

    //--- Get current prices
    double price = 0;
    string orderTypeStr = "";

    if(orderType == ORDER_TYPE_BUY)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        orderTypeStr = "BUY";
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        orderTypeStr = "SELL";
    }

    //--- Validate price
    if(price <= 0)
    {
        Print("Invalid price for ", orderTypeStr, " order: ", price);
        return;
    }

    //--- Calculate Stop Loss and Take Profit levels
    double sl = 0, tp = 0;
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    if(orderType == ORDER_TYPE_BUY)
    {
        if(InpStopLossPoints > 0)
            sl = price - (InpStopLossPoints * point);
        if(InpTakeProfitPoints > 0)
            tp = price + (InpTakeProfitPoints * point);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        if(InpStopLossPoints > 0)
            sl = price + (InpStopLossPoints * point);
        if(InpTakeProfitPoints > 0)
            tp = price - (InpTakeProfitPoints * point);
    }

    //--- Normalize prices
    if(sl > 0) sl = NormalizeDouble(sl, _Digits);
    if(tp > 0) tp = NormalizeDouble(tp, _Digits);

    //--- Prepare trade request
    string comment = InpTradeComment + " - " + reason;

    //--- Execute trade
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(InpLotSize, _Symbol, price, sl, tp, comment);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        result = trade.Sell(InpLotSize, _Symbol, price, sl, tp, comment);
    }

    //--- Check result
    if(result)
    {
        Print("✓ ", orderTypeStr, " trade opened successfully");
        Print("  Price: ", DoubleToString(price, _Digits));
        Print("  Lot Size: ", InpLotSize);
        if(sl > 0) Print("  Stop Loss: ", DoubleToString(sl, _Digits), " (", InpStopLossPoints, " points)");
        if(tp > 0) Print("  Take Profit: ", DoubleToString(tp, _Digits), " (", InpTakeProfitPoints, " points)");
        Print("  Reason: ", reason);

        isTradeOpen = true;
        currentTradeType = orderType;
    }
    else
    {
        Print("✗ Failed to open ", orderTypeStr, " trade");
        Print("  Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
        Print("  Price: ", DoubleToString(price, _Digits));
    }
}

//+------------------------------------------------------------------+
//| Close current trade                                              |
//+------------------------------------------------------------------+
void CloseTrade(string reason)
{
    if(!isTradeOpen)
        return;

    //--- Find and close all positions with our magic number
    bool foundTrade = false;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol && PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                foundTrade = true;
                ulong positionTicket = PositionGetInteger(POSITION_TICKET);

                if(trade.PositionClose(positionTicket))
                {
                    string positionTypeStr = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "BUY" : "SELL";
                    Print("✓ ", positionTypeStr, " trade closed successfully");
                    Print("  Ticket: ", positionTicket);
                    Print("  Reason: ", reason);

                    isTradeOpen = false;
                }
                else
                {
                    Print("✗ Failed to close trade");
                    Print("  Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
                    Print("  Ticket: ", positionTicket);
                }
            }
        }
    }

    if(!foundTrade)
    {
        Print("No trade found to close - resetting trade status");
        isTradeOpen = false;
    }
}

//+------------------------------------------------------------------+
//| Check for existing trades on startup                            |
//+------------------------------------------------------------------+
void CheckExistingTrades()
{
    isTradeOpen = false;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol && PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                isTradeOpen = true;
                currentTradeType = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

                string positionTypeStr = (currentTradeType == ORDER_TYPE_BUY) ? "BUY" : "SELL";
                Print("Found existing ", positionTypeStr, " trade on startup");
                Print("  Ticket: ", PositionGetInteger(POSITION_TICKET));
                Print("  Volume: ", PositionGetDouble(POSITION_VOLUME));

                break; // We only expect one trade at a time
            }
        }
    }

    if(!isTradeOpen)
    {
        Print("No existing trades found on startup");
    }
}



//+------------------------------------------------------------------+
//| Clean up signal arrows from chart                               |
//+------------------------------------------------------------------+
void CleanupSignalArrows()
{
    //--- Remove all signal arrows from chart
    int totalObjects = ObjectsTotal(0);
    for(int i = totalObjects - 1; i >= 0; i--)
    {
        string objName = ObjectName(0, i);
        if(StringFind(objName, "BuyArrow_") == 0 ||
           StringFind(objName, "SellArrow_") == 0)
        {
            ObjectDelete(0, objName);
        }
    }
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    //--- Monitor our trades
    if(trans.symbol == _Symbol &&
       request.magic == InpMagicNumber)
    {
        if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
        {
            // Trade was executed
            if(trans.deal_type == DEAL_TYPE_BUY)
            {
                Print("Trade Transaction: BUY deal executed, ticket: ", trans.deal);
            }
            else if(trans.deal_type == DEAL_TYPE_SELL)
            {
                Print("Trade Transaction: SELL deal executed, ticket: ", trans.deal);
            }
        }
        else if(trans.type == TRADE_TRANSACTION_POSITION)
        {
            // Position was closed or modified
            if(trans.order_state == ORDER_STATE_FILLED)
            {
                // Check if position still exists
                CheckExistingTrades();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Initialize Multi-Timeframe Analysis                             |
//+------------------------------------------------------------------+
void InitializeMultiTimeframe()
{
    //--- Set up timeframe periods
    MTF_Periods[0] = InpTF1;
    MTF_Periods[1] = InpTF2;
    MTF_Periods[2] = InpTF3;
    MTF_Periods[3] = InpTF4;
    MTF_Periods[4] = InpTF5;

    //--- Update timeframe names based on actual periods
    for(int i = 0; i < 5; i++)
    {
        MTF_Names[i] = TimeframeToString(MTF_Periods[i]);
    }

    //--- Initialize ATR handles for each timeframe
    for(int i = 0; i < 5; i++)
    {
        MTF_ATRHandles[i] = iATR(_Symbol, MTF_Periods[i], InpLength);
        if(MTF_ATRHandles[i] == INVALID_HANDLE)
        {
            Print("Failed to create ATR handle for ", MTF_Names[i]);
        }
        else
        {
            Print("ATR handle created for ", MTF_Names[i]);
        }

        // No need for complex arrays - we'll calculate trends on demand

        MTF_TrendValues[i] = 0; // Initialize trend as neutral
    }
}

//+------------------------------------------------------------------+
//| Convert timeframe to string                                     |
//+------------------------------------------------------------------+
string TimeframeToString(ENUM_TIMEFRAMES tf)
{
    switch(tf)
    {
        case PERIOD_M1:  return "M1";
        case PERIOD_M5:  return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1:  return "H1";
        case PERIOD_H4:  return "H4";
        case PERIOD_D1:  return "D1";
        case PERIOD_W1:  return "W1";
        case PERIOD_MN1: return "MN1";
        default: return "Unknown";
    }
}

//+------------------------------------------------------------------+
//| Update Multi-Timeframe Table                                    |
//+------------------------------------------------------------------+
void UpdateMultiTimeframeTable()
{
    //--- Calculate trends for all timeframes
    for(int i = 0; i < 5; i++)
    {
        MTF_TrendValues[i] = CalculateTimeframeTrend(i);
    }

    //--- Create/Update the visual table
    CreateMTFTable();
}

//+------------------------------------------------------------------+
//| Calculate trend for specific timeframe                          |
//+------------------------------------------------------------------+
int CalculateTimeframeTrend(int tfIndex)
{
    if(MTF_ATRHandles[tfIndex] == INVALID_HANDLE)
        return 0;

    ENUM_TIMEFRAMES tf = MTF_Periods[tfIndex];
    int bars = MathMax(InpLength * 2, 100);

    //--- Get price data for this timeframe
    double close[];
    if(CopyClose(_Symbol, tf, 0, bars, close) <= 0)
        return 0;

    //--- Get ATR data for this timeframe
    double atr[];
    if(CopyBuffer(MTF_ATRHandles[tfIndex], 0, 0, bars, atr) <= 0)
        return 0;

    //--- Set arrays as normal (not time series)
    ArraySetAsSeries(close, false);
    ArraySetAsSeries(atr, false);

    //--- Simple calculation for current trend only
    if(bars < InpLength + 10)
        return 0;

    //--- Calculate Zero Lag EMA for the most recent bars only
    double zlemaBuffer[];
    ArrayResize(zlemaBuffer, bars);
    ArrayInitialize(zlemaBuffer, 0);

    //--- Calculate starting position
    int start = InpLength;

    //--- Calculate Zero Lag EMA for recent bars
    for(int i = start; i < bars; i++)
    {
        int lag = (InpLength - 1) / 2;
        double lagCompensatedPrice = 0;
        if(i >= lag)
            lagCompensatedPrice = close[i] + (close[i] - close[i - lag]);
        else
            lagCompensatedPrice = close[i];

        if(i == InpLength)
        {
            // Initialize with SMA
            double sum = 0;
            for(int j = 0; j < InpLength; j++)
            {
                int idx = i - j;
                double tempLagPrice = (idx >= lag) ? close[idx] + (close[idx] - close[idx - lag]) : close[idx];
                sum += tempLagPrice;
            }
            zlemaBuffer[i] = sum / InpLength;
        }
        else if(i > InpLength)
        {
            // EMA calculation
            double alpha = 2.0 / (InpLength + 1.0);
            zlemaBuffer[i] = alpha * lagCompensatedPrice + (1.0 - alpha) * zlemaBuffer[i-1];
        }
        else
        {
            zlemaBuffer[i] = close[i];
        }
    }

    //--- Calculate trend for the most recent bar
    int lastBar = bars - 1;
    double zlema = zlemaBuffer[lastBar];

    //--- Calculate volatility (simple max ATR)
    double maxATR = 0;
    int lookback = MathMin(InpLength * 3, bars - 1);
    for(int i = 0; i < lookback; i++)
    {
        int idx = lastBar - i;
        if(idx >= 0 && atr[idx] > maxATR)
            maxATR = atr[idx];
    }

    double volatility = maxATR * InpMultiplier;

    //--- Determine trend
    if(close[lastBar] > zlema + volatility)
        return 1;  // Bullish
    else if(close[lastBar] < zlema - volatility)
        return -1; // Bearish
    else
        return 0;  // Neutral
}



//+------------------------------------------------------------------+
//| Create Multi-Timeframe Table                                    |
//+------------------------------------------------------------------+
void CreateMTFTable()
{
    //--- Table parameters
    string tableName = "MTF_Table";
    int x = 20;
    int y = 50;
    int width = 200;
    int height = 150;

    //--- Delete existing table
    ObjectDelete(0, tableName);

    //--- Create background rectangle
    if(ObjectCreate(0, tableName, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, tableName, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, tableName, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, tableName, OBJPROP_XSIZE, width);
        ObjectSetInteger(0, tableName, OBJPROP_YSIZE, height);
        ObjectSetInteger(0, tableName, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, tableName, OBJPROP_BGCOLOR, clrNavy);
        ObjectSetInteger(0, tableName, OBJPROP_BORDER_COLOR, clrWhite);
        ObjectSetInteger(0, tableName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Create title
    string titleName = "MTF_Title";
    ObjectDelete(0, titleName);
    if(ObjectCreate(0, titleName, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, titleName, OBJPROP_XDISTANCE, x + 10);
        ObjectSetInteger(0, titleName, OBJPROP_YDISTANCE, y + 10);
        ObjectSetString(0, titleName, OBJPROP_TEXT, "Zero Lag MTF Signals");
        ObjectSetInteger(0, titleName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, titleName, OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, titleName, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, titleName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Create timeframe labels
    for(int i = 0; i < 5; i++)
    {
        string labelName = "MTF_Label_" + IntegerToString(i);
        ObjectDelete(0, labelName);

        if(ObjectCreate(0, labelName, OBJ_LABEL, 0, 0, 0))
        {
            ObjectSetInteger(0, labelName, OBJPROP_XDISTANCE, x + 10);
            ObjectSetInteger(0, labelName, OBJPROP_YDISTANCE, y + 35 + (i * 20));

            string trendText = "";
            color trendColor = clrGray;

            if(MTF_TrendValues[i] == 1)
            {
                trendText = "BULLISH";
                trendColor = InpBullishColor;
            }
            else if(MTF_TrendValues[i] == -1)
            {
                trendText = "BEARISH";
                trendColor = InpBearishColor;
            }
            else
            {
                trendText = "NEUTRAL";
                trendColor = clrGray;
            }

            string displayText = MTF_Names[i] + ": " + trendText;
            ObjectSetString(0, labelName, OBJPROP_TEXT, displayText);
            ObjectSetInteger(0, labelName, OBJPROP_COLOR, trendColor);
            ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 9);
            ObjectSetString(0, labelName, OBJPROP_FONT, "Arial");
            ObjectSetInteger(0, labelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }
    }

    ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| Clean up MTF Table                                              |
//+------------------------------------------------------------------+
void CleanupMTFTable()
{
    //--- Delete table objects
    ObjectDelete(0, "MTF_Table");
    ObjectDelete(0, "MTF_Title");

    for(int i = 0; i < 5; i++)
    {
        ObjectDelete(0, "MTF_Label_" + IntegerToString(i));
    }
}
