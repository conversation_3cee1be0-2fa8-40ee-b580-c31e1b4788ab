//+------------------------------------------------------------------+
//|                                                   fxZeroTrend EA |
//|                                  Copyright 2025, AlgoAlpha Port |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AlgoAlpha Port"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade/Trade.mqh>

//--- Input parameters for the EA
input group "=== Trading Settings ==="
input double InpLotSize = 0.01;              // Lot Size
input int InpSlippagePoints = 10;             // Slippage in Points
input int InpMagicNumber = 123456;            // Magic Number
input string InpTradeComment = "fxZeroTrend"; // Trade Comment

input group "=== Zero Lag Settings ==="
input int InpLength = 70;                    // Zero Lag EMA Length
input double InpMultiplier = 1.2;            // Volatility Band Multiplier
input bool InpShowSignals = true;            // Show Buy/Sell Arrows on Chart
input bool InpEnableAlerts = true;           // Enable Alerts
input color InpBullishColor = clrLime;       // Bullish Color
input color InpBearishColor = clrRed;        // Bearish Color

//--- Global variables
CTrade trade;
int ATRHandle;
double ZLEMABuffer[];
double ATRBuffer[];
int TrendState[];
datetime lastSignalTime = 0;
bool isTradeOpen = false;
ENUM_ORDER_TYPE currentTradeType = ORDER_TYPE_BUY;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize the trade object
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetDeviationInPoints(InpSlippagePoints);
    
    //--- Create ATR handle
    ATRHandle = iATR(_Symbol, _Period, InpLength);
    if(ATRHandle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator");
        return INIT_FAILED;
    }
    
    //--- Initialize arrays
    ArraySetAsSeries(ZLEMABuffer, true);
    ArraySetAsSeries(ATRBuffer, true);
    ArraySetAsSeries(TrendState, true);
    
    //--- Check for existing trades
    CheckExistingTrades();
    
    Print("fxZeroTrend EA initialized successfully");
    Print("Magic Number: ", InpMagicNumber);
    Print("Lot Size: ", InpLotSize);
    Print("Zero Lag Length: ", InpLength);
    Print("Multiplier: ", InpMultiplier);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release ATR handle
    if(ATRHandle != INVALID_HANDLE)
        IndicatorRelease(ATRHandle);
        
    //--- Clean up chart objects
    if(InpShowSignals)
        CleanupSignalArrows();
        
    Print("fxZeroTrend EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    
    if(currentBarTime == lastBarTime)
        return; // No new bar, exit
        
    lastBarTime = currentBarTime;
    
    //--- Calculate Zero Lag signals
    if(!CalculateZeroLagSignals())
        return;
    
    //--- Check for trading signals
    CheckForSignals();
}

//+------------------------------------------------------------------+
//| Calculate Zero Lag EMA and signals                              |
//+------------------------------------------------------------------+
bool CalculateZeroLagSignals()
{
    //--- Get required bars
    int bars = MathMax(InpLength * 4, 200);

    //--- Get price data (NOT as time series to match indicator)
    double close[];
    if(CopyClose(_Symbol, _Period, 0, bars, close) <= 0)
    {
        Print("Error copying close prices");
        return false;
    }

    //--- Get ATR data (NOT as time series to match indicator)
    if(CopyBuffer(ATRHandle, 0, 0, bars, ATRBuffer) <= 0)
    {
        Print("Error copying ATR data");
        return false;
    }

    //--- Resize arrays
    ArrayResize(ZLEMABuffer, bars);
    ArrayResize(TrendState, bars);

    //--- DO NOT set as time series - use normal array indexing like indicator
    ArraySetAsSeries(close, false);
    ArraySetAsSeries(ZLEMABuffer, false);
    ArraySetAsSeries(ATRBuffer, false);
    ArraySetAsSeries(TrendState, false);

    //--- Initialize arrays
    ArrayInitialize(ZLEMABuffer, 0);
    ArrayInitialize(TrendState, 0);

    //--- Calculate starting position (same as indicator)
    int start = InpLength;
    if(start < InpLength) start = InpLength;

    //--- Main calculation loop (same direction as indicator)
    for(int i = start; i < bars; i++)
    {
        //--- Calculate Zero Lag EMA using exact same method as indicator
        int lag = (InpLength - 1) / 2;
        double lagCompensatedPrice = 0;
        if(i >= lag)
            lagCompensatedPrice = close[i] + (close[i] - close[i - lag]);
        else
            lagCompensatedPrice = close[i];

        if(i == InpLength)
        {
            // Initialize with SMA (same as indicator)
            double sum = 0;
            for(int j = 0; j < InpLength; j++)
            {
                int idx = i - j;
                double tempLagPrice = (idx >= lag) ? close[idx] + (close[idx] - close[idx - lag]) : close[idx];
                sum += tempLagPrice;
            }
            ZLEMABuffer[i] = sum / InpLength;
        }
        else if(i > InpLength)
        {
            // EMA calculation (same as indicator)
            double alpha = 2.0 / (InpLength + 1.0);
            ZLEMABuffer[i] = alpha * lagCompensatedPrice + (1.0 - alpha) * ZLEMABuffer[i-1];
        }
        else
        {
            ZLEMABuffer[i] = close[i];
        }

        double zlema = ZLEMABuffer[i];
        double volatility = CalculateVolatility(i) * InpMultiplier;

        //--- Determine trend (same as indicator)
        int trend = 0;
        if(i > 0)
            trend = TrendState[i-1];

        if(close[i] > zlema + volatility)
            trend = 1;
        else if(close[i] < zlema - volatility)
            trend = -1;

        TrendState[i] = trend;

        //--- Debug print for recent bars
        if(i >= bars - 3)
        {
            Print("Bar ", i, " (", bars-1-i, " from current): Close=", close[i], " ZLEMA=", zlema,
                  " Volatility=", volatility, " Trend=", trend);
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Zero Lag EMA                                          |
//+------------------------------------------------------------------+
double CalculateZLEMA(const double &price[], int pos, int period)
{
    //--- Not enough data for calculation
    if(pos > ArraySize(price) - period - 1)
        return price[pos];

    int lag = (period - 1) / 2;
    double alpha = 2.0 / (period + 1.0);

    //--- Get lag-compensated price
    double lagPrice = price[pos];
    if(pos + lag < ArraySize(price))
        lagPrice = price[pos] + (price[pos] - price[pos + lag]);

    //--- Initialize with SMA for first calculation (oldest bar)
    if(pos == ArraySize(price) - period - 1)
    {
        double sum = 0;
        int count = 0;
        for(int i = 0; i < period; i++)
        {
            int idx = pos + i;
            if(idx < ArraySize(price))
            {
                double tempLagPrice = price[idx];
                if(idx + lag < ArraySize(price))
                    tempLagPrice = price[idx] + (price[idx] - price[idx + lag]);
                sum += tempLagPrice;
                count++;
            }
        }
        return (count > 0) ? sum / count : price[pos];
    }

    //--- EMA calculation using previous ZLEMA value
    if(pos + 1 < ArraySize(ZLEMABuffer) && ZLEMABuffer[pos + 1] != 0)
        return alpha * lagPrice + (1 - alpha) * ZLEMABuffer[pos + 1];
    else
        return lagPrice; // Fallback
}

//+------------------------------------------------------------------+
//| Calculate Volatility (Highest ATR) - Same as indicator          |
//+------------------------------------------------------------------+
double CalculateVolatility(int pos)
{
    if(pos < InpLength * 3) return (pos < ArraySize(ATRBuffer)) ? ATRBuffer[pos] : 0.001;

    double maxATR = 0;
    int lookback = InpLength * 3;

    for(int i = 0; i < lookback && (pos - i) >= 0; i++)
    {
        if(ATRBuffer[pos - i] > maxATR)
            maxATR = ATRBuffer[pos - i];
    }

    return (maxATR > 0) ? maxATR : 0.001;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckForSignals()
{
    //--- Check if we have enough data
    int arraySize = ArraySize(TrendState);
    if(arraySize < 3)
    {
        Print("Not enough trend data: ", arraySize);
        return;
    }

    //--- Get current time for signal validation
    datetime currentTime = iTime(_Symbol, _Period, 1); // Previous completed bar

    //--- Avoid duplicate signals on the same bar
    if(currentTime <= lastSignalTime)
        return;

    //--- With normal arrays, the most recent bars are at the end
    int currentBar = arraySize - 1;      // Most recent completed bar
    int previousBar = arraySize - 2;     // Previous bar

    //--- Debug: Print current trend states
    if(arraySize >= 3)
    {
        Print("Signal Check - Recent trends: [", previousBar, "]=", TrendState[previousBar],
              " [", currentBar, "]=", TrendState[currentBar]);
    }

    //--- Check for trend change signals
    // Bullish trend change: from non-bullish to bullish
    if(currentBar >= 0 && previousBar >= 0 &&
       TrendState[currentBar] == 1 && TrendState[previousBar] != 1)
    {
        Print("*** BULLISH TREND CHANGE DETECTED ***");
        Print("Previous bar trend: ", TrendState[previousBar], " -> Current completed bar trend: ", TrendState[currentBar]);
        Print("Time: ", TimeToString(currentTime));

        //--- Close any existing bearish trade
        if(isTradeOpen && currentTradeType == ORDER_TYPE_SELL)
            CloseTrade("Opposite signal detected");

        //--- Open bullish trade if no trade is open
        if(!isTradeOpen)
            OpenTrade(ORDER_TYPE_BUY, "Bullish Trend Change");

        lastSignalTime = currentTime;

        if(InpEnableAlerts)
            Alert("[fxZeroTrend] Bullish trend change on ", _Symbol);
    }

    // Bearish trend change: from non-bearish to bearish
    if(currentBar >= 0 && previousBar >= 0 &&
       TrendState[currentBar] == -1 && TrendState[previousBar] != -1)
    {
        Print("*** BEARISH TREND CHANGE DETECTED ***");
        Print("Previous bar trend: ", TrendState[previousBar], " -> Current completed bar trend: ", TrendState[currentBar]);
        Print("Time: ", TimeToString(currentTime));

        //--- Close any existing bullish trade
        if(isTradeOpen && currentTradeType == ORDER_TYPE_BUY)
            CloseTrade("Opposite signal detected");

        //--- Open bearish trade if no trade is open
        if(!isTradeOpen)
            OpenTrade(ORDER_TYPE_SELL, "Bearish Trend Change");

        lastSignalTime = currentTime;

        if(InpEnableAlerts)
            Alert("[fxZeroTrend] Bearish trend change on ", _Symbol);
    }
}

//+------------------------------------------------------------------+
//| Open a trade                                                     |
//+------------------------------------------------------------------+
void OpenTrade(ENUM_ORDER_TYPE orderType, string reason)
{
    //--- Check if trading is allowed
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        Print("Trading is not allowed in terminal");
        return;
    }

    if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
    {
        Print("Trading is not allowed for this EA");
        return;
    }

    //--- Get current prices
    double price = 0;
    string orderTypeStr = "";

    if(orderType == ORDER_TYPE_BUY)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        orderTypeStr = "BUY";
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        orderTypeStr = "SELL";
    }

    //--- Validate price
    if(price <= 0)
    {
        Print("Invalid price for ", orderTypeStr, " order: ", price);
        return;
    }

    //--- Prepare trade request
    string comment = InpTradeComment + " - " + reason;

    //--- Execute trade
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(InpLotSize, _Symbol, price, 0, 0, comment);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        result = trade.Sell(InpLotSize, _Symbol, price, 0, 0, comment);
    }

    //--- Check result
    if(result)
    {
        Print("✓ ", orderTypeStr, " trade opened successfully");
        Print("  Price: ", DoubleToString(price, _Digits));
        Print("  Lot Size: ", InpLotSize);
        Print("  Reason: ", reason);

        isTradeOpen = true;
        currentTradeType = orderType;
    }
    else
    {
        Print("✗ Failed to open ", orderTypeStr, " trade");
        Print("  Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
        Print("  Price: ", DoubleToString(price, _Digits));
    }
}

//+------------------------------------------------------------------+
//| Close current trade                                              |
//+------------------------------------------------------------------+
void CloseTrade(string reason)
{
    if(!isTradeOpen)
        return;

    //--- Find and close all positions with our magic number
    bool foundTrade = false;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol && PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                foundTrade = true;
                ulong positionTicket = PositionGetInteger(POSITION_TICKET);

                if(trade.PositionClose(positionTicket))
                {
                    string positionTypeStr = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "BUY" : "SELL";
                    Print("✓ ", positionTypeStr, " trade closed successfully");
                    Print("  Ticket: ", positionTicket);
                    Print("  Reason: ", reason);

                    isTradeOpen = false;
                }
                else
                {
                    Print("✗ Failed to close trade");
                    Print("  Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
                    Print("  Ticket: ", positionTicket);
                }
            }
        }
    }

    if(!foundTrade)
    {
        Print("No trade found to close - resetting trade status");
        isTradeOpen = false;
    }
}

//+------------------------------------------------------------------+
//| Check for existing trades on startup                            |
//+------------------------------------------------------------------+
void CheckExistingTrades()
{
    isTradeOpen = false;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol && PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                isTradeOpen = true;
                currentTradeType = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

                string positionTypeStr = (currentTradeType == ORDER_TYPE_BUY) ? "BUY" : "SELL";
                Print("Found existing ", positionTypeStr, " trade on startup");
                Print("  Ticket: ", PositionGetInteger(POSITION_TICKET));
                Print("  Volume: ", PositionGetDouble(POSITION_VOLUME));

                break; // We only expect one trade at a time
            }
        }
    }

    if(!isTradeOpen)
    {
        Print("No existing trades found on startup");
    }
}

//+------------------------------------------------------------------+
//| Show signal arrows on chart                                     |
//+------------------------------------------------------------------+
void ShowSignalOnChart(int pos, int trend, double price, double zlema, double volatility)
{
    if(!InpShowSignals)
        return;

    datetime barTime = iTime(_Symbol, _Period, pos);

    //--- Check for trend change signals
    if(pos < ArraySize(TrendState) - 1)
    {
        // Bullish trend change
        if(trend == 1 && TrendState[pos + 1] != 1)
        {
            string arrowName = "BuyArrow_" + IntegerToString(barTime);
            double arrowPrice = zlema - volatility * 0.8;

            if(ObjectCreate(0, arrowName, OBJ_ARROW_UP, 0, barTime, arrowPrice))
            {
                ObjectSetInteger(0, arrowName, OBJPROP_COLOR, InpBullishColor);
                ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 3);
                ObjectSetString(0, arrowName, OBJPROP_TEXT, "BUY");
            }
        }

        // Bearish trend change
        if(trend == -1 && TrendState[pos + 1] != -1)
        {
            string arrowName = "SellArrow_" + IntegerToString(barTime);
            double arrowPrice = zlema + volatility * 0.8;

            if(ObjectCreate(0, arrowName, OBJ_ARROW_DOWN, 0, barTime, arrowPrice))
            {
                ObjectSetInteger(0, arrowName, OBJPROP_COLOR, InpBearishColor);
                ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 3);
                ObjectSetString(0, arrowName, OBJPROP_TEXT, "SELL");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Clean up signal arrows from chart                               |
//+------------------------------------------------------------------+
void CleanupSignalArrows()
{
    //--- Remove all signal arrows from chart
    for(int i = ObjectsTotal(0, 0, OBJ_ARROW_UP) - 1; i >= 0; i--)
    {
        string objName = ObjectName(0, i, 0, OBJ_ARROW_UP);
        if(StringFind(objName, "BuyArrow_") == 0)
            ObjectDelete(0, objName);
    }

    for(int i = ObjectsTotal(0, 0, OBJ_ARROW_DOWN) - 1; i >= 0; i--)
    {
        string objName = ObjectName(0, i, 0, OBJ_ARROW_DOWN);
        if(StringFind(objName, "SellArrow_") == 0)
            ObjectDelete(0, objName);
    }
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    //--- Monitor our trades
    if(trans.symbol == _Symbol &&
       request.magic == InpMagicNumber)
    {
        if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
        {
            // Trade was executed
            if(trans.deal_type == DEAL_TYPE_BUY)
            {
                Print("Trade Transaction: BUY deal executed, ticket: ", trans.deal);
            }
            else if(trans.deal_type == DEAL_TYPE_SELL)
            {
                Print("Trade Transaction: SELL deal executed, ticket: ", trans.deal);
            }
        }
        else if(trans.type == TRADE_TRANSACTION_POSITION)
        {
            // Position was closed or modified
            if(trans.order_state == ORDER_STATE_FILLED)
            {
                // Check if position still exists
                CheckExistingTrades();
            }
        }
    }
}
