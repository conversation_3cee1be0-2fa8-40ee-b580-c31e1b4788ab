# Zero Lag Trend Signals MT5 Indicator

This is an MT5 port of the TradingView "Zero Lag Trend Signals (MTF)" indicator by AlgoAlpha.

## Features

### Visual Elements (Same as TradingView Version)
- **Zero Lag EMA Line**: Color-coded trend line (Green = Bullish, Red = Bearish)
- **Dynamic Volatility Bands**: Upper/Lower bands that appear based on trend direction
- **Large Trend Arrows**: 
  - ▲ Green arrows for bullish trend changes
  - ▼ Red arrows for bearish trend changes
- **Small Entry Arrows**:
  - ▲ Small green arrows for bullish entry signals
  - ▼ Small red arrows for bearish entry signals
- **Multi-Timeframe Table**: Shows trend status across 5 timeframes

### Signal Logic
1. **Trend Changes** (Large Arrows):
   - Bullish: Price breaks above upper volatility band
   - Bearish: Price breaks below lower volatility band

2. **Entry Signals** (Small Arrows):
   - Bullish Entry: Price crosses above ZLEMA during established bullish trend
   - Bearish Entry: Price crosses below ZLEMA during established bearish trend

3. **Zero Lag EMA Calculation**:
   - Uses lag compensation: `EMA(price + (price - price[lag]), length)`
   - Reduces traditional EMA lag

4. **Volatility Bands**:
   - Based on highest ATR over lookback period
   - Multiplied by user-defined factor

## Installation Instructions

1. **Copy the indicator file**:
   - Copy `ZeroLagTrendSignals.mq5` to your MT5 `MQL5/Indicators/` folder
   - Location is usually: `C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\[TerminalID]\MQL5\Indicators\`

2. **Compile the indicator**:
   - Open MetaEditor (F4 in MT5)
   - Open the `ZeroLagTrendSignals.mq5` file
   - Press F7 to compile
   - Fix any compilation errors if they occur

3. **Add to chart**:
   - In MT5, go to Insert → Indicators → Custom → ZeroLagTrendSignals
   - Or drag from Navigator panel to chart

## Input Parameters

- **Length** (default: 70): Lookback period for Zero Lag EMA
- **Band Multiplier** (default: 1.2): Controls volatility band thickness
- **Timeframes 1-5**: Multi-timeframe analysis periods
- **Colors**: Bullish and bearish signal colors
- **Show Table**: Enable/disable multi-timeframe table
- **Enable Alerts**: Enable/disable alert notifications

## Alert Types

The indicator provides alerts for:
- Bullish/Bearish trend changes
- Bullish/Bearish entry signals
- Multi-timeframe trend changes

## Usage Tips

1. **Trend Following**: Use large arrows to identify major trend changes
2. **Entry Timing**: Use small arrows for precise entry points within trends
3. **Multi-Timeframe Confirmation**: Check the table for alignment across timeframes
4. **Risk Management**: Consider volatility bands for stop-loss placement

## Differences from TradingView Version

- Multi-timeframe table shows in comment area (can be enhanced with GUI objects)
- Alert system uses MT5's native alert functionality
- Color coding follows MT5 conventions
- Some visual elements may appear slightly different due to platform differences

## Troubleshooting

1. **Compilation Errors**: Ensure you're using MT5 build 3550 or later
2. **No Signals**: Check that sufficient historical data is available
3. **Performance**: Reduce lookback periods if indicator is slow
4. **Alerts Not Working**: Ensure alerts are enabled in MT5 settings

## Support

This indicator replicates the core functionality of the original TradingView indicator. For questions about the trading logic, refer to the original AlgoAlpha documentation.
