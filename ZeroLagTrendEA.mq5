//+------------------------------------------------------------------+
//|                                            Zero Lag Trend EA    |
//|                                  Copyright 2025, AlgoAlpha Port |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AlgoAlpha Port"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade/Trade.mqh>

//--- Input parameters for the EA
input group "=== Trading Settings ==="
input double InpLotSize = 0.01;              // Lot Size
input int InpSlippagePoints = 10;             // Slippage in Points
input int InpMagicNumber = 123456;            // Magic Number
input string InpTradeComment = "ZeroLagEA";   // Trade Comment

input group "=== Indicator Settings ==="
input int InpLength = 70;                    // Length for Zero Lag EMA
input double InpMultiplier = 1.2;            // Band Multiplier
input ENUM_TIMEFRAMES InpTF1 = PERIOD_M5;    // Timeframe 1
input ENUM_TIMEFRAMES InpTF2 = PERIOD_M15;   // Timeframe 2
input ENUM_TIMEFRAMES InpTF3 = PERIOD_H1;    // Timeframe 3
input ENUM_TIMEFRAMES InpTF4 = PERIOD_H4;    // Timeframe 4
input ENUM_TIMEFRAMES InpTF5 = PERIOD_D1;    // Timeframe 5
input color InpBullishColor = clrLime;       // Bullish Color
input color InpBearishColor = clrRed;        // Bearish Color
input bool InpShowTable = false;             // Show Multi-Timeframe Table (EA mode)
input bool InpEnableAlerts = false;          // Enable Alerts (EA mode)
input bool InpShowPriceLabels = false;       // Show Price Labels (EA mode)

//--- Global variables
CTrade trade;
int indicatorHandle;
double bullishTrendBuffer[];    // Buffer 3 - Large bullish arrows
double bearishTrendBuffer[];    // Buffer 4 - Large bearish arrows
double bullishEntryBuffer[];    // Buffer 5 - Small bullish arrows (ignored)
double bearishEntryBuffer[];    // Buffer 6 - Small bearish arrows (ignored)
datetime lastSignalTime = 0;
bool isTradeOpen = false;
ENUM_ORDER_TYPE currentTradeType = ORDER_TYPE_BUY; // Track current trade direction

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize the trade object
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetDeviationInPoints(InpSlippagePoints);
    
    //--- Create indicator handle for Zero Lag Trend Signals
    indicatorHandle = iCustom(_Symbol, _Period, "ZeroLagTrendSignals",
                              InpLength, InpMultiplier, InpTF1, InpTF2, InpTF3, InpTF4, InpTF5,
                              InpBullishColor, InpBearishColor, InpShowTable, InpEnableAlerts, InpShowPriceLabels);
    
    if(indicatorHandle == INVALID_HANDLE)
    {
        Print("Error creating Zero Lag Trend Signals indicator handle");
        return INIT_FAILED;
    }
    
    //--- Resize arrays
    ArraySetAsSeries(bullishTrendBuffer, true);
    ArraySetAsSeries(bearishTrendBuffer, true);
    ArraySetAsSeries(bullishEntryBuffer, true);
    ArraySetAsSeries(bearishEntryBuffer, true);
    
    //--- Check if there's already an open trade with our magic number
    CheckExistingTrades();
    
    Print("Zero Lag Trend EA initialized successfully");
    Print("Magic Number: ", InpMagicNumber);
    Print("Lot Size: ", InpLotSize);
    Print("Monitoring large arrows only (trend changes)");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handle
    if(indicatorHandle != INVALID_HANDLE)
        IndicatorRelease(indicatorHandle);
        
    Print("Zero Lag Trend EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    
    if(currentBarTime == lastBarTime)
        return; // No new bar, exit
        
    lastBarTime = currentBarTime;
    
    //--- Get indicator data
    if(CopyBuffer(indicatorHandle, 3, 0, 3, bullishTrendBuffer) < 3 ||  // Large bullish arrows
       CopyBuffer(indicatorHandle, 4, 0, 3, bearishTrendBuffer) < 3)    // Large bearish arrows
    {
        Print("Error copying indicator buffers");
        return;
    }
    
    //--- Check for new signals on the completed bar (index 1)
    CheckForSignals();
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckForSignals()
{
    //--- Get current time for signal validation
    datetime currentTime = iTime(_Symbol, _Period, 1); // Previous completed bar
    
    //--- Avoid duplicate signals on the same bar
    if(currentTime <= lastSignalTime)
        return;
    
    //--- Check for large bullish arrow (trend change to bullish)
    if(bullishTrendBuffer[1] != EMPTY_VALUE && bullishTrendBuffer[1] != 0)
    {
        Print("Large Bullish Arrow detected at ", TimeToString(currentTime));
        
        //--- Close any existing bearish trade first
        if(isTradeOpen && currentTradeType == ORDER_TYPE_SELL)
        {
            CloseTrade("Opposite signal detected");
        }
        
        //--- Open bullish trade if no trade is open
        if(!isTradeOpen)
        {
            OpenTrade(ORDER_TYPE_BUY, "Large Bullish Arrow Signal");
        }
        
        lastSignalTime = currentTime;
    }
    
    //--- Check for large bearish arrow (trend change to bearish)  
    if(bearishTrendBuffer[1] != EMPTY_VALUE && bearishTrendBuffer[1] != 0)
    {
        Print("Large Bearish Arrow detected at ", TimeToString(currentTime));
        
        //--- Close any existing bullish trade first
        if(isTradeOpen && currentTradeType == ORDER_TYPE_BUY)
        {
            CloseTrade("Opposite signal detected");
        }
        
        //--- Open bearish trade if no trade is open
        if(!isTradeOpen)
        {
            OpenTrade(ORDER_TYPE_SELL, "Large Bearish Arrow Signal");
        }
        
        lastSignalTime = currentTime;
    }
}

//+------------------------------------------------------------------+
//| Open a trade                                                     |
//+------------------------------------------------------------------+
void OpenTrade(ENUM_ORDER_TYPE orderType, string reason)
{
    //--- Check if trading is allowed
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        Print("Trading is not allowed in terminal");
        return;
    }
    
    if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
    {
        Print("Trading is not allowed for this EA");
        return;
    }
    
    //--- Get current prices
    double price = 0;
    string orderTypeStr = "";
    
    if(orderType == ORDER_TYPE_BUY)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        orderTypeStr = "BUY";
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        orderTypeStr = "SELL";
    }
    
    //--- Validate price
    if(price <= 0)
    {
        Print("Invalid price for ", orderTypeStr, " order: ", price);
        return;
    }
    
    //--- Prepare trade request
    string comment = InpTradeComment + " - " + reason;
    
    //--- Execute trade
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(InpLotSize, _Symbol, price, 0, 0, comment);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        result = trade.Sell(InpLotSize, _Symbol, price, 0, 0, comment);
    }
    
    //--- Check result
    if(result)
    {
        Print("✓ ", orderTypeStr, " trade opened successfully");
        Print("  Price: ", DoubleToString(price, _Digits));
        Print("  Lot Size: ", InpLotSize);
        Print("  Reason: ", reason);
        
        isTradeOpen = true;
        currentTradeType = orderType;
    }
    else
    {
        Print("✗ Failed to open ", orderTypeStr, " trade");
        Print("  Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
        Print("  Price: ", DoubleToString(price, _Digits));
    }
}

//+------------------------------------------------------------------+
//| Close current trade                                              |
//+------------------------------------------------------------------+
void CloseTrade(string reason)
{
    if(!isTradeOpen)
        return;
    
    //--- Find and close all positions with our magic number
    bool foundTrade = false;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol && PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                foundTrade = true;
                ulong positionTicket = PositionGetInteger(POSITION_TICKET);
                
                if(trade.PositionClose(positionTicket))
                {
                    string positionTypeStr = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "BUY" : "SELL";
                    Print("✓ ", positionTypeStr, " trade closed successfully");
                    Print("  Ticket: ", positionTicket);
                    Print("  Reason: ", reason);
                    
                    isTradeOpen = false;
                }
                else
                {
                    Print("✗ Failed to close trade");
                    Print("  Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
                    Print("  Ticket: ", positionTicket);
                }
            }
        }
    }
    
    if(!foundTrade)
    {
        Print("No trade found to close - resetting trade status");
        isTradeOpen = false;
    }
}

//+------------------------------------------------------------------+
//| Check for existing trades on startup                            |
//+------------------------------------------------------------------+
void CheckExistingTrades()
{
    isTradeOpen = false;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol && PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                isTradeOpen = true;
                currentTradeType = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
                
                string positionTypeStr = (currentTradeType == ORDER_TYPE_BUY) ? "BUY" : "SELL";
                Print("Found existing ", positionTypeStr, " trade on startup");
                Print("  Ticket: ", PositionGetInteger(POSITION_TICKET));
                Print("  Volume: ", PositionGetDouble(POSITION_VOLUME));
                
                break; // We only expect one trade at a time
            }
        }
    }
    
    if(!isTradeOpen)
    {
        Print("No existing trades found on startup");
    }
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    //--- Monitor our trades
    if(trans.symbol == _Symbol && 
       request.magic == InpMagicNumber)
    {
        if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
        {
            // Trade was executed
            if(trans.deal_type == DEAL_TYPE_BUY)
            {
                Print("Trade Transaction: BUY deal executed, ticket: ", trans.deal);
            }
            else if(trans.deal_type == DEAL_TYPE_SELL)
            {
                Print("Trade Transaction: SELL deal executed, ticket: ", trans.deal);
            }
        }
        else if(trans.type == TRADE_TRANSACTION_POSITION)
        {
            // Position was closed or modified
            if(trans.order_state == ORDER_STATE_FILLED)
            {
                // Check if position still exists
                CheckExistingTrades();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Get indicator buffer value safely                               |
//+------------------------------------------------------------------+
double GetIndicatorValue(int bufferIndex, int shift)
{
    double buffer[1];
    if(CopyBuffer(indicatorHandle, bufferIndex, shift, 1, buffer) > 0)
        return buffer[0];
    return EMPTY_VALUE;
}
