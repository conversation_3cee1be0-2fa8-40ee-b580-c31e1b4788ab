'fxZeroTrend.mq5'			0
Trade.mqh			
Object.mqh			
StdLibErr.mqh			
OrderInfo.mqh			
HistoryOrderInfo.mqh			
PositionInfo.mqh			
DealInfo.mqh			
'[' - invalid index value	fxZeroTrend.mq5	47	27
'[' - invalid index value	fxZeroTrend.mq5	48	25
'[' - invalid index value	fxZeroTrend.mq5	49	23
'MTF_ZLEMABuffers' - invalid array access	fxZeroTrend.mq5	684	21
'[' - parameter conversion not allowed	fxZeroTrend.mq5	684	37
   built-in: int ArrayResize(void&,int,int)	fxZeroTrend.mq5	684	37
'MTF_ATRBuffers' - invalid array access	fxZeroTrend.mq5	685	21
'[' - parameter conversion not allowed	fxZeroTrend.mq5	685	35
   built-in: int ArrayResize(void&,int,int)	fxZeroTrend.mq5	685	35
'MTF_TrendStates' - invalid array access	fxZeroTrend.mq5	686	21
'[' - parameter conversion not allowed	fxZeroTrend.mq5	686	36
   built-in: int ArrayResize(void&,int,int)	fxZeroTrend.mq5	686	36
'MTF_ZLEMABuffers' - invalid array access	fxZeroTrend.mq5	687	26
'[' - parameter conversion not allowed	fxZeroTrend.mq5	687	42
   built-in: bool ArraySetAsSeries(const void&,bool)	fxZeroTrend.mq5	687	42
'MTF_ATRBuffers' - invalid array access	fxZeroTrend.mq5	688	26
'[' - parameter conversion not allowed	fxZeroTrend.mq5	688	40
   built-in: bool ArraySetAsSeries(const void&,bool)	fxZeroTrend.mq5	688	40
'MTF_TrendStates' - invalid array access	fxZeroTrend.mq5	689	26
'[' - parameter conversion not allowed	fxZeroTrend.mq5	689	41
   built-in: bool ArraySetAsSeries(const void&,bool)	fxZeroTrend.mq5	689	41
'MTF_ATRBuffers' - invalid array access	fxZeroTrend.mq5	747	56
'CopyBuffer' - no one of the overloads can be applied to the function call	fxZeroTrend.mq5	747	8
could be one of 3 function(s)	fxZeroTrend.mq5	747	8
   built-in: int CopyBuffer(int,int,int,int,double&[])	fxZeroTrend.mq5	747	8
   built-in: int CopyBuffer(int,int,datetime,int,double&[])	fxZeroTrend.mq5	747	8
   built-in: int CopyBuffer(int,int,datetime,datetime,double&[])	fxZeroTrend.mq5	747	8
'MTF_ZLEMABuffers' - invalid array access	fxZeroTrend.mq5	752	17
'[' - parameter conversion not allowed	fxZeroTrend.mq5	752	33
   built-in: int ArrayResize(void&,int,int)	fxZeroTrend.mq5	752	33
'MTF_TrendStates' - invalid array access	fxZeroTrend.mq5	753	17
'[' - parameter conversion not allowed	fxZeroTrend.mq5	753	32
   built-in: int ArrayResize(void&,int,int)	fxZeroTrend.mq5	753	32
'MTF_ZLEMABuffers' - invalid array access	fxZeroTrend.mq5	754	21
'[' - parameter conversion not allowed	fxZeroTrend.mq5	754	37
   built-in: int ArrayInitialize(void&,void)	fxZeroTrend.mq5	754	37
'MTF_TrendStates' - invalid array access	fxZeroTrend.mq5	755	21
'[' - parameter conversion not allowed	fxZeroTrend.mq5	755	36
   built-in: int ArrayInitialize(void&,void)	fxZeroTrend.mq5	755	36
'MTF_ATRBuffers' - invalid array access	fxZeroTrend.mq5	820	33
'[' - parameter conversion not allowed	fxZeroTrend.mq5	820	47
   built-in: int ArraySize(const void&)	fxZeroTrend.mq5	820	47
27 errors, 0 warnings		27	0
