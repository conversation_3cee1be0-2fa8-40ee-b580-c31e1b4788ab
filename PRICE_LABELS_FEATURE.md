# Price Labels Feature Added to Zero Lag MT5 Indicator

## What's New

I've enhanced the MT5 Zero Lag Trend Signals indicator to display **price labels** on the large trend change arrows, showing exactly where you should enter trades.

## Features Added:

### 1. **Price Labels on Large Arrows**
- **BUY Labels**: Show the exact price when bullish trend change occurs
- **SELL Labels**: Show the exact price when bearish trend change occurs
- **Format**: "BUY 1.2345" or "SELL 1.2345" (using actual market price)

### 2. **Visual Positioning**
- **Bullish labels**: Positioned above the entry price with green color
- **Bearish labels**: Positioned below the entry price with red color
- **Font**: Small, readable 8pt font that doesn't clutter the chart

### 3. **Control Options**
- **New Input Parameter**: `InpShowPriceLabels` (true/false)
- You can turn price labels on/off without affecting arrows
- Labels automatically clean up when indicator is removed

### 4. **Enhanced Alerts**
- Alerts now include the exact entry price
- Example: "Bullish Trend Change at 1.2345 on EURUSD H1"

## How It Works:

### Large Arrows (Main Signals) Now Show:
1. **Large ▲ Arrow**: Trend changes to bullish
   - **Price Label**: "BUY [price]" - This is your exact entry price
   
2. **Large ▼ Arrow**: Trend changes to bearish  
   - **Price Label**: "SELL [price]" - This is your exact entry price

### Small Arrows (Pullbacks):
- Still show as before for pullback entries
- No price labels (to avoid chart clutter)
- These are secondary signals within the established trend

## Trading Usage:

1. **Wait for Large Arrow**: This signals the main trend change
2. **Note the Price Label**: This is your exact entry price
3. **Enter Trade**: At the price shown on the label
4. **Manage Risk**: Use the volatility bands for stop-loss placement

## Installation:

1. Copy the updated `ZeroLagTrendSignals.mq5` to your MT5 indicators folder
2. Compile in MetaEditor
3. Add to chart
4. In settings, ensure `InpShowPriceLabels = true`

## Example:
When a bullish trend change occurs at price 1.23456:
- Large green ▲ arrow appears
- Green "BUY 1.23456" label shows exactly where to enter
- Alert says "Bullish Trend Change at 1.23456"

This gives you precise entry points for your trades, eliminating guesswork about where the signal occurred!
