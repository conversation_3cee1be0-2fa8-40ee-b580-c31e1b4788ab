# How to Remove Price Labels from MT5 Zero Lag Indicator

## Problem
When you set "Show Price Labels on Large Arrows" to **false** in the indicator settings, the existing price labels (like "BUY 3381.480", "SELL 3376.362") remain on the chart.

## Solution

I've updated the indicator code to automatically clean up existing labels when you change the setting to false. Here's what happens now:

### ✅ **Automatic Cleanup Added:**
1. **Setting Change Detection**: The indicator now monitors when you change the price label setting
2. **Immediate Cleanup**: When you set it to `false`, it automatically removes all existing labels
3. **Real-time Response**: No need to restart or reload the indicator

### 🔧 **How to Remove Existing Labels:**

#### **Method 1: Change the Setting (Recommended)**
1. Open indicator properties
2. Set "Show Price Labels on Large Arrows" to **false**  
3. Click **OK**
4. The labels will automatically disappear

#### **Method 2: Manual Cleanup (If needed)**
If for some reason the automatic cleanup doesn't work:
1. Remove the indicator from the chart
2. Re-add it with "Show Price Labels" set to **false**

#### **Method 3: Chart Object Cleanup**
1. Right-click on chart → **Objects** → **Objects List**
2. Delete all objects starting with "BullSignal_" or "BearSignal_"

### 🎯 **Code Changes Made:**

1. **Added Tracking Variable:**
   ```mql5
   bool PrevShowLabels = true;  // Track previous state
   ```

2. **Added Cleanup Detection:**
   ```mql5
   if(!InpShowPriceLabels && PrevShowLabels)
   {
       CleanupPriceLabels();  // Remove all existing labels
   }
   ```

3. **Enhanced CleanupPriceLabels() Function:**
   - Automatically removes all "BullSignal_" and "BearSignal_" objects
   - Called when setting changes from true to false
   - Also called when indicator is removed

### 🚀 **What This Fixes:**
- ✅ **Immediate Response**: Labels disappear when setting is changed to false
- ✅ **Clean Chart**: No need to manually remove objects
- ✅ **Real-time**: Works instantly without indicator restart
- ✅ **Memory Efficient**: Prevents label accumulation

### 📝 **Usage:**
- **Show Labels**: Set to `true` - new signals will show price labels
- **Hide Labels**: Set to `false` - existing labels are removed, new signals won't show labels

The updated indicator now properly responds to the setting change and keeps your chart clean!
