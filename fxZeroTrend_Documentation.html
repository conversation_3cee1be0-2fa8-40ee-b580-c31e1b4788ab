<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>fxZeroTrend EA - Complete Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .feature-box {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #27ae60;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .parameter-table th,
        .parameter-table td {
            border: 1px solid #bdc3c7;
            padding: 12px;
            text-align: left;
        }
        .parameter-table th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        .parameter-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
        }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #2980b9;
            text-decoration: none;
            font-weight: 500;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .mtf-example {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">📈</span> fxZeroTrend EA - Complete User Guide</h1>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#overview">1. Overview</a></li>
                <li><a href="#features">2. Key Features</a></li>
                <li><a href="#parameters">3. Input Parameters</a></li>
                <li><a href="#mtf-confirmation">4. Multi-Timeframe Confirmation</a></li>
                <li><a href="#risk-management">5. Risk Management</a></li>
                <li><a href="#slippage">6. Understanding Slippage</a></li>
                <li><a href="#setup">7. Setup Guide</a></li>
                <li><a href="#examples">8. Configuration Examples</a></li>
                <li><a href="#troubleshooting">9. Troubleshooting</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2><span class="emoji">🎯</span> Overview</h2>
            <div class="feature-box">
                <p><strong>fxZeroTrend</strong> is an advanced Expert Advisor that combines Zero Lag EMA calculations with multi-timeframe analysis for intelligent trend-following trades. The EA is specifically optimized for XAUUSD (Gold) trading but works on all currency pairs.</p>
            </div>
            
            <h3>Core Trading Logic</h3>
            <ul>
                <li><strong>Zero Lag EMA:</strong> Uses lag-compensated exponential moving average for faster signal detection</li>
                <li><strong>Volatility Bands:</strong> ATR-based bands determine trend strength and entry points</li>
                <li><strong>Trend Detection:</strong> Bullish when price > ZLEMA + Volatility, Bearish when price < ZLEMA - Volatility</li>
                <li><strong>Signal Generation:</strong> Trades on trend changes from neutral/opposite to bullish/bearish</li>
            </ul>
        </section>

        <section id="features">
            <h2><span class="emoji">⭐</span> Key Features</h2>
            
            <div class="success-box">
                <h3><span class="emoji">🔧</span> Self-Contained Design</h3>
                <p>No external indicator dependencies - all calculations are embedded directly in the EA for maximum reliability and speed.</p>
            </div>

            <div class="feature-box">
                <h3><span class="emoji">📊</span> Multi-Timeframe Analysis</h3>
                <ul>
                    <li>Analyzes 5 configurable timeframes simultaneously</li>
                    <li>Visual table showing trend direction for each timeframe</li>
                    <li>Color-coded display: Green (Bullish), Red (Bearish), Gray (Neutral)</li>
                </ul>
            </div>

            <div class="feature-box">
                <h3><span class="emoji">🎯</span> Smart Signal Confirmation</h3>
                <ul>
                    <li>Optional multi-timeframe confirmation before trade execution</li>
                    <li>Requires 2+ timeframes in same direction for trade approval</li>
                    <li>Waits for neutral timeframes to confirm rather than blocking trades</li>
                </ul>
            </div>

            <div class="feature-box">
                <h3><span class="emoji">📍</span> Visual Signal Display</h3>
                <ul>
                    <li>Buy/Sell arrows displayed directly on chart</li>
                    <li>Arrows persist through chart refreshes and timeframe changes</li>
                    <li>Customizable arrow colors</li>
                </ul>
            </div>
        </section>

        <section id="parameters">
            <h2><span class="emoji">⚙️</span> Input Parameters</h2>
            
            <h3>Trading Settings</h3>
            <table class="parameter-table">
                <tr>
                    <th>Parameter</th>
                    <th>Default</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>InpLotSize</td>
                    <td>0.01</td>
                    <td>Trade size in lots</td>
                </tr>
                <tr>
                    <td>InpSlippagePoints</td>
                    <td>10</td>
                    <td>Maximum acceptable price slippage in points</td>
                </tr>
                <tr>
                    <td>InpMagicNumber</td>
                    <td>123456</td>
                    <td>Unique identifier for EA trades</td>
                </tr>
                <tr>
                    <td>InpTradeComment</td>
                    <td>"fxZeroTrend"</td>
                    <td>Comment added to all trades</td>
                </tr>
                <tr>
                    <td>InpTFConfirm</td>
                    <td>true</td>
                    <td>Enable/disable multi-timeframe confirmation</td>
                </tr>
            </table>

            <h3>Risk Management</h3>
            <table class="parameter-table">
                <tr>
                    <th>Parameter</th>
                    <th>Default</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>InpTakeProfitPoints</td>
                    <td>0</td>
                    <td>Take profit distance in points (0 = disabled)</td>
                </tr>
                <tr>
                    <td>InpStopLossPoints</td>
                    <td>0</td>
                    <td>Stop loss distance in points (0 = disabled)</td>
                </tr>
            </table>

            <h3>Zero Lag Settings</h3>
            <table class="parameter-table">
                <tr>
                    <th>Parameter</th>
                    <th>Default</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>InpLength</td>
                    <td>70</td>
                    <td>Zero Lag EMA calculation period</td>
                </tr>
                <tr>
                    <td>InpMultiplier</td>
                    <td>1.2</td>
                    <td>Volatility band multiplier (higher = wider bands)</td>
                </tr>
                <tr>
                    <td>InpShowSignals</td>
                    <td>true</td>
                    <td>Display buy/sell arrows on chart</td>
                </tr>
                <tr>
                    <td>InpEnableAlerts</td>
                    <td>true</td>
                    <td>Enable popup alerts for new signals</td>
                </tr>
                <tr>
                    <td>InpBullishColor</td>
                    <td>Lime</td>
                    <td>Color for bullish signals</td>
                </tr>
                <tr>
                    <td>InpBearishColor</td>
                    <td>Red</td>
                    <td>Color for bearish signals</td>
                </tr>
            </table>

            <h3>Multi-Timeframe Settings</h3>
            <table class="parameter-table">
                <tr>
                    <th>Parameter</th>
                    <th>Default</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>InpShowMTFTable</td>
                    <td>true</td>
                    <td>Display multi-timeframe analysis table</td>
                </tr>
                <tr>
                    <td>InpTF1</td>
                    <td>M5</td>
                    <td>First timeframe for analysis</td>
                </tr>
                <tr>
                    <td>InpTF2</td>
                    <td>M15</td>
                    <td>Second timeframe for analysis</td>
                </tr>
                <tr>
                    <td>InpTF3</td>
                    <td>H1</td>
                    <td>Third timeframe for analysis</td>
                </tr>
                <tr>
                    <td>InpTF4</td>
                    <td>H4</td>
                    <td>Fourth timeframe for analysis</td>
                </tr>
                <tr>
                    <td>InpTF5</td>
                    <td>D1</td>
                    <td>Fifth timeframe for analysis</td>
                </tr>
            </table>
        </section>

        <section id="mtf-confirmation">
            <h2><span class="emoji">🕐</span> Multi-Timeframe Confirmation</h2>

            <div class="warning-box">
                <h3><span class="emoji">⚠️</span> Important: TFConfirm Setting</h3>
                <p>The <span class="highlight">InpTFConfirm</span> parameter controls whether trades require multi-timeframe confirmation before execution.</p>
            </div>

            <h3>When TFConfirm = false</h3>
            <ul>
                <li>EA works exactly as a single-timeframe system</li>
                <li>All signals are taken immediately</li>
                <li>No multi-timeframe analysis required</li>
                <li>Faster execution, more trades</li>
            </ul>

            <h3>When TFConfirm = true (Recommended)</h3>
            <div class="feature-box">
                <h4>Confirmation Rules:</h4>
                <ul>
                    <li><strong>✅ 2+ Confirming Timeframes:</strong> Trade executed immediately</li>
                    <li><strong>⏳ Neutral Timeframes Present:</strong> Signal pending, wait for confirmation</li>
                    <li><strong>❌ Opposing Timeframes:</strong> Signal rejected</li>
                </ul>
            </div>

            <h3>Understanding Trend States</h3>
            <div class="mtf-example">
                <strong>BULLISH:</strong> Price > Zero Lag EMA + Volatility Band<br>
                <strong>BEARISH:</strong> Price < Zero Lag EMA - Volatility Band<br>
                <strong>NEUTRAL:</strong> Price between volatility bands (consolidation/indecision)
            </div>

            <h3>Example Scenarios</h3>

            <div class="success-box">
                <h4>Scenario 1: Immediate Confirmation</h4>
                <div class="mtf-example">
Signal: BULLISH trend change detected<br>
MTF Analysis:<br>
  M5:  BULLISH  ✓<br>
  M15: BULLISH  ✓<br>
  H1:  BULLISH  ✓<br>
  H4:  NEUTRAL<br>
  D1:  NEUTRAL<br>
Result: ✅ Trade immediately (3 confirming TFs)
                </div>
            </div>

            <div class="warning-box">
                <h4>Scenario 2: Pending Confirmation</h4>
                <div class="mtf-example">
Signal: BULLISH trend change detected<br>
MTF Analysis:<br>
  M5:  BULLISH  ✓<br>
  M15: NEUTRAL  ⏳<br>
  H1:  NEUTRAL  ⏳<br>
  H4:  NEUTRAL  ⏳<br>
  D1:  NEUTRAL  ⏳<br>
Result: ⏳ Wait for neutrals to turn bullish
                </div>
            </div>

            <div class="feature-box">
                <h4>Scenario 3: Signal Rejection</h4>
                <div class="mtf-example">
Signal: BULLISH trend change detected<br>
MTF Analysis:<br>
  M5:  BULLISH  ✓<br>
  M15: BEARISH  ❌<br>
  H1:  BEARISH  ❌<br>
  H4:  BEARISH  ❌<br>
  D1:  BEARISH  ❌<br>
Result: ❌ Reject signal (opposing higher TFs)
                </div>
            </div>

            <h3>Pending Signal Management</h3>
            <ul>
                <li><strong>Wait Period:</strong> Up to 5 bars for confirmation</li>
                <li><strong>Automatic Monitoring:</strong> Checks every new bar for confirmation</li>
                <li><strong>Signal Expiry:</strong> Pending signals expire after 5 bars</li>
                <li><strong>Priority:</strong> New signals cancel old pending signals</li>
            </ul>
        </section>

        <section id="risk-management">
            <h2><span class="emoji">🛡️</span> Risk Management</h2>

            <h3>Take Profit & Stop Loss</h3>
            <div class="feature-box">
                <p>The EA includes traditional take profit and stop loss settings measured in <strong>points</strong>.</p>
            </div>

            <h3>How It Works</h3>
            <div class="code-block">
Buy Orders:
  Take Profit = Entry Price + (InpTakeProfitPoints × Point)
  Stop Loss   = Entry Price - (InpStopLossPoints × Point)

Sell Orders:
  Take Profit = Entry Price - (InpTakeProfitPoints × Point)
  Stop Loss   = Entry Price + (InpStopLossPoints × Point)
            </div>

            <h3>Points vs Pips Guide</h3>
            <table class="parameter-table">
                <tr>
                    <th>Broker Type</th>
                    <th>Example Pair</th>
                    <th>Points per Pip</th>
                    <th>50 Pips TP Setting</th>
                </tr>
                <tr>
                    <td>5-Digit</td>
                    <td>EURUSD</td>
                    <td>10</td>
                    <td>500 points</td>
                </tr>
                <tr>
                    <td>4-Digit</td>
                    <td>EURUSD</td>
                    <td>10</td>
                    <td>500 points</td>
                </tr>
                <tr>
                    <td>3-Digit</td>
                    <td>USDJPY</td>
                    <td>1</td>
                    <td>50 points</td>
                </tr>
                <tr>
                    <td>Gold</td>
                    <td>XAUUSD</td>
                    <td>10</td>
                    <td>500 points</td>
                </tr>
            </table>

            <h3>Configuration Examples</h3>
            <div class="success-box">
                <h4>Conservative Setup (1:2 Risk/Reward)</h4>
                <ul>
                    <li>Take Profit: 200 points</li>
                    <li>Stop Loss: 100 points</li>
                    <li>Good for stable market conditions</li>
                </ul>
            </div>

            <div class="feature-box">
                <h4>Aggressive Setup (1:3 Risk/Reward)</h4>
                <ul>
                    <li>Take Profit: 300 points</li>
                    <li>Stop Loss: 100 points</li>
                    <li>Suitable for trending markets</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>No Fixed Levels (Manual Management)</h4>
                <ul>
                    <li>Take Profit: 0 (disabled)</li>
                    <li>Stop Loss: 0 (disabled)</li>
                    <li>User manages trades manually</li>
                </ul>
            </div>
        </section>

        <section id="slippage">
            <h2><span class="emoji">⚡</span> Understanding Slippage</h2>

            <div class="feature-box">
                <h3>What is Slippage?</h3>
                <p>Slippage is the difference between the expected price of a trade and the actual execution price. The <span class="highlight">InpSlippagePoints</span> parameter sets the maximum acceptable slippage.</p>
            </div>

            <h3>How Slippage Works in fxZeroTrend</h3>
            <div class="code-block">
Buy Order Example:
  Signal Price: 1.2500
  Slippage: 10 points

  Acceptable Range:
  - Best Case: 1.2500 (exact price)
  - Worst Case: 1.2501 (1.2500 + 10 points)

  If market moves to 1.2502 → Order REJECTED
  If market moves to 1.2501 → Order EXECUTED
            </div>

            <h3>Why Slippage Matters</h3>
            <ul>
                <li><strong>Signal-Based Trading:</strong> EA trades immediately on trend changes</li>
                <li><strong>Market Orders:</strong> Uses market orders for instant execution</li>
                <li><strong>Volatile Markets:</strong> Protects against bad fills during news events</li>
                <li><strong>MTF Delays:</strong> Price can move during confirmation process</li>
            </ul>

            <h3>Slippage Configuration Guide</h3>
            <table class="parameter-table">
                <tr>
                    <th>Setting</th>
                    <th>Points</th>
                    <th>Pros</th>
                    <th>Cons</th>
                </tr>
                <tr>
                    <td>Conservative</td>
                    <td>5-10</td>
                    <td>Better prices, lower costs</td>
                    <td>More rejections, missed signals</td>
                </tr>
                <tr>
                    <td>Moderate</td>
                    <td>10-20</td>
                    <td>Good balance, most signals filled</td>
                    <td>Reasonable slippage costs</td>
                </tr>
                <tr>
                    <td>Aggressive</td>
                    <td>20-50</td>
                    <td>High fill rate, few rejections</td>
                    <td>Higher costs, worse prices</td>
                </tr>
            </table>

            <div class="warning-box">
                <h3><span class="emoji">⚠️</span> When to Adjust Slippage</h3>
                <h4>Increase Slippage (15-30 points) if:</h4>
                <ul>
                    <li>Trading during high-impact news events</li>
                    <li>Using volatile pairs (GBP/JPY, etc.)</li>
                    <li>Experiencing many order rejections</li>
                    <li>Trading during low liquidity hours</li>
                </ul>

                <h4>Decrease Slippage (5-10 points) if:</h4>
                <ul>
                    <li>Trading major pairs during liquid hours</li>
                    <li>Using high-quality broker with tight spreads</li>
                    <li>Experiencing excessive slippage costs</li>
                    <li>Backtesting shows high slippage impact</li>
                </ul>
            </div>
        </section>

        <section id="setup">
            <h2><span class="emoji">🚀</span> Setup Guide</h2>

            <h3>Step 1: Installation</h3>
            <ol>
                <li>Copy <code>fxZeroTrend.mq5</code> to your <code>MQL5/Experts</code> folder</li>
                <li>Open MetaEditor and compile the EA</li>
                <li>Restart MetaTrader 5</li>
                <li>The EA should appear in the Navigator under "Expert Advisors"</li>
            </ol>

            <h3>Step 2: Basic Configuration</h3>
            <div class="success-box">
                <h4>Recommended Starting Settings:</h4>
                <ul>
                    <li><strong>Lot Size:</strong> 0.01 (micro lot for testing)</li>
                    <li><strong>TF Confirmation:</strong> true (enabled)</li>
                    <li><strong>Take Profit:</strong> 200 points</li>
                    <li><strong>Stop Loss:</strong> 100 points</li>
                    <li><strong>Slippage:</strong> 10 points</li>
                </ul>
            </div>

            <h3>Step 3: Timeframe Setup</h3>
            <div class="feature-box">
                <h4>Recommended Timeframe Configurations:</h4>

                <h5>Day Trading Setup:</h5>
                <ul>
                    <li>TF1: M5, TF2: M15, TF3: H1, TF4: H4, TF5: D1</li>
                    <li>Chart Timeframe: M15 or M30</li>
                </ul>

                <h5>Swing Trading Setup:</h5>
                <ul>
                    <li>TF1: H1, TF2: H4, TF3: D1, TF4: W1, TF5: MN1</li>
                    <li>Chart Timeframe: H4 or D1</li>
                </ul>

                <h5>Scalping Setup:</h5>
                <ul>
                    <li>TF1: M1, TF2: M5, TF3: M15, TF4: M30, TF5: H1</li>
                    <li>Chart Timeframe: M5 or M15</li>
                </ul>
            </div>

            <h3>Step 4: Testing</h3>
            <ol>
                <li><strong>Strategy Tester:</strong> Backtest with historical data first</li>
                <li><strong>Demo Account:</strong> Test on demo with real market conditions</li>
                <li><strong>Small Live:</strong> Start with minimum lot size on live account</li>
                <li><strong>Monitor & Adjust:</strong> Fine-tune settings based on performance</li>
            </ol>
        </section>

        <section id="examples">
            <h2><span class="emoji">📊</span> Configuration Examples</h2>

            <div class="success-box">
                <h3>Gold (XAUUSD) - Conservative</h3>
                <div class="code-block">
Lot Size: 0.01
Take Profit: 300 points (30 pips)
Stop Loss: 150 points (15 pips)
Slippage: 15 points
TF Confirmation: true
Timeframes: M15, H1, H4, D1, W1
Zero Lag Length: 70
Multiplier: 1.2
                </div>
            </div>

            <div class="feature-box">
                <h3>EURUSD - Aggressive</h3>
                <div class="code-block">
Lot Size: 0.05
Take Profit: 500 points (50 pips)
Stop Loss: 200 points (20 pips)
Slippage: 10 points
TF Confirmation: false
Timeframes: M5, M15, M30, H1, H4
Zero Lag Length: 50
Multiplier: 1.0
                </div>
            </div>

            <div class="warning-box">
                <h3>GBPJPY - High Volatility</h3>
                <div class="code-block">
Lot Size: 0.01
Take Profit: 800 points (80 pips)
Stop Loss: 300 points (30 pips)
Slippage: 25 points
TF Confirmation: true
Timeframes: M15, H1, H4, D1, W1
Zero Lag Length: 100
Multiplier: 1.5
                </div>
            </div>
        </section>

        <section id="troubleshooting">
            <h2><span class="emoji">🔧</span> Troubleshooting</h2>

            <h3>Common Issues</h3>

            <div class="warning-box">
                <h4>No Trades Being Taken</h4>
                <ul>
                    <li>Check if TF Confirmation is blocking trades</li>
                    <li>Verify timeframes are set correctly</li>
                    <li>Ensure trading is allowed in terminal</li>
                    <li>Check if slippage is too restrictive</li>
                    <li>Verify sufficient account balance</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Arrows Not Showing</h4>
                <ul>
                    <li>Ensure InpShowSignals = true</li>
                    <li>Check if chart allows objects</li>
                    <li>Refresh chart (F5)</li>
                    <li>Restart EA</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>MTF Table Not Visible</h4>
                <ul>
                    <li>Ensure InpShowMTFTable = true</li>
                    <li>Check top-left corner of chart</li>
                    <li>Verify timeframes are valid</li>
                    <li>Restart EA if needed</li>
                </ul>
            </div>

            <h3>Performance Optimization</h3>
            <ul>
                <li><strong>Reduce Timeframes:</strong> Use fewer MTF timeframes for faster processing</li>
                <li><strong>Adjust Length:</strong> Shorter Zero Lag length = more signals, longer = fewer signals</li>
                <li><strong>Optimize Multiplier:</strong> Lower = more sensitive, higher = less sensitive</li>
                <li><strong>Monitor Logs:</strong> Check Expert tab for detailed information</li>
            </ul>

            <h3>Support Information</h3>
            <div class="feature-box">
                <p><strong>EA Version:</strong> 1.00<br>
                <strong>Copyright:</strong> 2025, cFox<br>
                <strong>Optimized for:</strong> XAUUSD (Gold)<br>
                <strong>Compatible with:</strong> All currency pairs and timeframes</p>
            </div>
        </section>

        <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #bdc3c7; color: #7f8c8d;">
            <p><strong>fxZeroTrend EA</strong> - Advanced Multi-Timeframe Trading System</p>
            <p>© 2025 cFox. All rights reserved.</p>
        </footer>
    </div>
</body>
</html>
