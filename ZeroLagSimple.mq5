//+------------------------------------------------------------------+
//|                                   Zero Lag Trend Signals Simple |
//|                                  Copyright 2025, AlgoAlpha Port |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AlgoAlpha Port"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   5

// Plot 1: Zero Lag EMA
#property indicator_label1  "ZLEMA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrAqua
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

// Plot 2: Upper Band
#property indicator_label2  "Upper Band"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_style2  STYLE_DOT

// Plot 3: Lower Band  
#property indicator_label3  "Lower Band"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrLime
#property indicator_style3  STYLE_DOT

// Plot 4: Trend Signals
#property indicator_label4  "Trend Signals"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrYellow
#property indicator_width4  3

// Plot 5: Entry Signals
#property indicator_label5  "Entry Signals"
#property indicator_type5   DRAW_ARROW  
#property indicator_color5  clrWhite
#property indicator_width5  2

//--- Input parameters
input int    Length = 70;           // Zero Lag EMA Length
input double Multiplier = 1.2;      // Volatility Band Multiplier
input bool   ShowAlerts = true;     // Show Alerts
input color  BullColor = clrLime;   // Bullish Color
input color  BearColor = clrRed;    // Bearish Color

//--- Indicator buffers
double ZLEMABuffer[];
double UpperBandBuffer[];
double LowerBandBuffer[];
double TrendSignalBuffer[];
double EntrySignalBuffer[];

//--- Global variables
int ATRHandle;
int TrendDirection = 0;
int PrevTrend = 0;
datetime LastAlert = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set indicator buffers
   SetIndexBuffer(0, ZLEMABuffer, INDICATOR_DATA);
   SetIndexBuffer(1, UpperBandBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, LowerBandBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, TrendSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, EntrySignalBuffer, INDICATOR_DATA);
   
   //--- Set arrow symbols
   PlotIndexSetInteger(3, PLOT_ARROW, 159); // Large arrows for trend
   PlotIndexSetInteger(4, PLOT_ARROW, 159); // Small arrows for entry
   
   //--- Create ATR handle
   ATRHandle = iATR(_Symbol, _Period, Length);
   if(ATRHandle == INVALID_HANDLE)
   {
      Print("Failed to create ATR indicator");
      return(INIT_FAILED);
   }
   
   //--- Set starting point
   PlotIndexSetInteger(0, PLOT_DRAW_BEGIN, Length);
   PlotIndexSetInteger(1, PLOT_DRAW_BEGIN, Length);
   PlotIndexSetInteger(2, PLOT_DRAW_BEGIN, Length);
   PlotIndexSetInteger(3, PLOT_DRAW_BEGIN, Length);
   PlotIndexSetInteger(4, PLOT_DRAW_BEGIN, Length);
   
   //--- Initialize empty values
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(4, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(ATRHandle != INVALID_HANDLE)
      IndicatorRelease(ATRHandle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < Length * 2) return(0);
   
   //--- Get ATR values
   double atr[];
   if(CopyBuffer(ATRHandle, 0, 0, rates_total, atr) <= 0) return(0);
   
   //--- Calculate from
   int start = prev_calculated;
   if(start < Length) start = Length;
   
   //--- Main calculation loop
   for(int i = start; i < rates_total; i++)
   {
      //--- Calculate Zero Lag EMA
      double zlema = CalculateZLEMA(close, i);
      ZLEMABuffer[i] = zlema;
      
      //--- Calculate volatility bands
      double volatility = GetMaxATR(atr, i) * Multiplier;
      double upper_band = zlema + volatility;
      double lower_band = zlema - volatility;
      
      //--- Determine trend
      PrevTrend = TrendDirection;
      
      if(close[i] > upper_band)
         TrendDirection = 1;  // Bullish
      else if(close[i] < lower_band)
         TrendDirection = -1; // Bearish
      
      //--- Set band visibility based on trend
      if(TrendDirection == 1)
      {
         UpperBandBuffer[i] = EMPTY_VALUE;
         LowerBandBuffer[i] = lower_band;
         PlotIndexSetInteger(0, PLOT_LINE_COLOR, BullColor);
      }
      else if(TrendDirection == -1)
      {
         UpperBandBuffer[i] = upper_band;
         LowerBandBuffer[i] = EMPTY_VALUE;
         PlotIndexSetInteger(0, PLOT_LINE_COLOR, BearColor);
      }
      else
      {
         UpperBandBuffer[i] = EMPTY_VALUE;
         LowerBandBuffer[i] = EMPTY_VALUE;
      }
      
      //--- Reset signal buffers
      TrendSignalBuffer[i] = EMPTY_VALUE;
      EntrySignalBuffer[i] = EMPTY_VALUE;
      
      //--- Check for trend change (large arrows)
      if(i > Length)
      {
         // Bullish trend change
         if(TrendDirection == 1 && PrevTrend != 1)
         {
            TrendSignalBuffer[i] = lower_band - volatility * 0.5;
            PlotIndexSetInteger(3, PLOT_LINE_COLOR, BullColor);
            
            if(ShowAlerts && i == rates_total - 1)
               SendAlert("Bullish Trend Change", time[i]);
         }
         
         // Bearish trend change  
         if(TrendDirection == -1 && PrevTrend != -1)
         {
            TrendSignalBuffer[i] = upper_band + volatility * 0.5;
            PlotIndexSetInteger(3, PLOT_LINE_COLOR, BearColor);
            
            if(ShowAlerts && i == rates_total - 1)
               SendAlert("Bearish Trend Change", time[i]);
         }
         
         //--- Check for entry signals (small arrows)
         // Bullish entry: price crosses above ZLEMA in bullish trend
         if(TrendDirection == 1 && i > 0)
         {
            if(close[i] > zlema && close[i-1] <= ZLEMABuffer[i-1])
            {
               EntrySignalBuffer[i] = lower_band - volatility;
               PlotIndexSetInteger(4, PLOT_LINE_COLOR, BullColor);
               
               if(ShowAlerts && i == rates_total - 1)
                  SendAlert("Bullish Entry Signal", time[i]);
            }
         }
         
         // Bearish entry: price crosses below ZLEMA in bearish trend
         if(TrendDirection == -1 && i > 0)
         {
            if(close[i] < zlema && close[i-1] >= ZLEMABuffer[i-1])
            {
               EntrySignalBuffer[i] = upper_band + volatility;
               PlotIndexSetInteger(4, PLOT_LINE_COLOR, BearColor);
               
               if(ShowAlerts && i == rates_total - 1)
                  SendAlert("Bearish Entry Signal", time[i]);
            }
         }
      }
   }
   
   return(rates_total);
}

//+------------------------------------------------------------------+
//| Calculate Zero Lag EMA                                           |
//+------------------------------------------------------------------+
double CalculateZLEMA(const double &price[], int pos)
{
   if(pos < Length) return(0);
   
   int lag = (Length - 1) / 2;
   double alpha = 2.0 / (Length + 1.0);
   
   // Lag compensation
   double lag_price = price[pos];
   if(pos >= lag)
      lag_price = price[pos] + (price[pos] - price[pos - lag]);
   
   // First calculation
   if(pos == Length)
   {
      double sum = 0;
      for(int i = 0; i < Length; i++)
      {
         double temp_price = price[pos - i];
         if((pos - i) >= lag)
            temp_price = price[pos - i] + (price[pos - i] - price[pos - i - lag]);
         sum += temp_price;
      }
      return(sum / Length);
   }
   
   // EMA calculation
   return(alpha * lag_price + (1 - alpha) * ZLEMABuffer[pos - 1]);
}

//+------------------------------------------------------------------+
//| Get Maximum ATR for volatility calculation                       |
//+------------------------------------------------------------------+
double GetMaxATR(const double &atr[], int pos)
{
   if(pos < Length * 3) return(atr[pos]);
   
   double max_atr = 0;
   int lookback = Length * 3;
   
   for(int i = 0; i < lookback; i++)
   {
      if(pos - i >= 0 && atr[pos - i] > max_atr)
         max_atr = atr[pos - i];
   }
   
   return(max_atr);
}

//+------------------------------------------------------------------+
//| Send Alert Function                                               |
//+------------------------------------------------------------------+
void SendAlert(string message, datetime current_time)
{
   if(current_time <= LastAlert) return;
   
   LastAlert = current_time;
   string alert_text = "[Zero Lag] " + message + " on " + _Symbol;
   
   Alert(alert_text);
   Print(alert_text);
}
